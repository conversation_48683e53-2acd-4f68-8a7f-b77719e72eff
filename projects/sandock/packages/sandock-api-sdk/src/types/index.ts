export interface ResponseVO<T> {
  success: boolean;
  code: number;
  message: string;
  data: T;
}

export interface HttpClientLike {
  get<T>(url: string): Promise<ResponseVO<T>>;
  post<T, B = unknown>(url: string, body?: B): Promise<ResponseVO<T>>;
  delete<T>(url: string): Promise<ResponseVO<T>>;
}

export interface SandockClientOptions {
  baseURL?: string; // defaults to DEFAULT_BASE_URL if omitted
  token?: string; // 未来鉴权
  userAgent?: string;
}

export interface MetaInfo {
  version: string;
  timestamp: number;
}

export interface SandboxCreateRequest {
  image?: string;
  pythonImage?: string;
  pull?: boolean;
  memoryLimitMb?: number;
  cpuShares?: number;
  workdir?: string;
  keep?: boolean;
}
export interface SandboxCreateResponse {
  id: string;
}
export interface SandboxStartResponse {
  id: string;
  started: boolean;
}
export interface SandboxStopResponse {
  id: string;
  stopped: boolean;
}

export interface RunCodeRequest {
  language: 'javascript' | 'typescript' | 'python';
  code: string;
  timeoutMs?: number;
  input?: string;
}

export interface ShellRequest {
  cmd: string | string[];
  timeoutMs?: number;
  workdir?: string;
  env?: Record<string, string>;
  input?: string;
}

export interface ExecutionResult {
  stdout: string;
  stderr: string;
  exitCode: number | null;
  timedOut: boolean;
  durationMs: number;
}

export interface FileWriteRequest {
  path: string;
  content: string;
  executable?: boolean;
}

export interface FileReadResponse {
  path: string;
  content: string;
}
export interface ListFilesResponse {
  path: string;
  entries: string[];
}

export interface SandboxListResponse {
  items: { id: string }[];
}
