{"name": "sandock", "version": "2.0.1-alpha.14", "description": "Official TypeScript OOP SDK for the Sandock API (sandbox orchestration & code execution)", "type": "module", "license": "MIT", "sideEffects": false, "exports": {".": "./src/index.ts", "./api": "./src/api/index.ts", "./types": "./src/types/index.ts"}, "scripts": {"build": "tsc -p tsconfig.json", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest"}, "dependencies": {"axios": "^1.7.7"}, "devDependencies": {"@types/node": "^20.14.12", "typescript": "^5.8.3", "vitest": "^3.2.4"}}