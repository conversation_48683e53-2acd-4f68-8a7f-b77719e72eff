'use client';

import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { DoubleSelect } from '@bika/domains/shared/client/components/double-select';
import { RewardItem } from '@bika/domains/user/client/user-settings-panel-referral';
import type { InvitationType, InviteMemberTab, SpaceSettingsUIModal } from '@bika/types/space/bo';
import { useSpaceContextForce } from '@bika/types/space/context';
import type { RoleVO } from '@bika/types/unit/vo';
import { Button, IconButton } from '@bika/ui/button';
import { Chip } from '@bika/ui/chip';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { ButtonGroup, Input, List, ListItem } from '@bika/ui/forms';
import CopyOutlined from '@bika/ui/icons/components/copy_outlined';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { Modal } from '@bika/ui/modal';
import { MultiSelect } from '@bika/ui/multi-select';
import { type OptionProps, SingleSelect } from '@bika/ui/single-select';
import { Skeleton } from '@bika/ui/skeleton';
import { Tab, TabList, TabPanel, Tabs } from '@bika/ui/tabs';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';
import { copyText } from '@bika/ui/utils';
import { NavHeader } from '@bika/ui/web-layout';
import _ from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { InviteUserByEmail } from './invite-user-by-email';

const { set } = _;

interface Props {
  roleId?: string;
  teamId?: string;
  title?: string;
  onClose?: () => void;
  visibleTabs?: ('LINK_INVITE' | 'EMAIL_INVITE')[];
}

export default function InviteMembersView(props: Props) {
  const { visibleTabs = ['LINK_INVITE', 'EMAIL_INVITE'] } = props;
  const spaceContext = useSpaceContextForce();
  const { t } = useLocale();
  const paramData = useMemo(() => {
    const modalObj = spaceContext.getUIModal();
    if (!modalObj) {
      return null;
    }
    const modal = modalObj as SpaceSettingsUIModal;
    if (modal.tab?.type !== 'SPACE_INVITE_MEMBER') {
      return null;
    }
    const targetTab = modal.tab as InviteMemberTab;
    return targetTab.props;
  }, [spaceContext]);
  const chosenRoleId = props.roleId || '';
  const [loading, setLoading] = useState(false);
  const [identify, setIdentify] = useState<InvitationType>('MEMBER');

  const spaceId = spaceContext.data.id;
  const spaceName = spaceContext.data.name;
  const permission = spaceContext.permission;
  const rootTeam = spaceContext.rootTeam;
  const guestRootTeam = spaceContext.guestRootTeam;
  const [teamId, setTeamId] = useState<string>(props.teamId || rootTeam.id);
  const [roles, setRoles] = useState<string[]>(chosenRoleId ? [chosenRoleId] : []);
  const { trpc, trpcQuery } = useApiCaller();
  const utils = trpcQuery.useUtils();

  const {
    data: invitations = [],
    refetch,
    isLoading: isLoadingLinkInvitation,
  } = trpcQuery.linkInvitation.list.useQuery({ spaceId });
  const deleteLinkInvitation = trpcQuery.linkInvitation.delete.useMutation();
  const createLinkInvitation = trpcQuery.linkInvitation.create.useMutation();

  const { data: referralCode, isLoading: isLoadingReferralCode } =
    trpcQuery.user.referralCode.useQuery();
  const { data: userCoins } = trpcQuery.user.coins.useQuery();
  const userReferralCode = referralCode?.userReferralCode;
  const isLoading = isLoadingLinkInvitation || isLoadingReferralCode;

  useEffect(() => {
    if (paramData?.teamId) {
      setTeamId(paramData.teamId);
    }
  }, [paramData?.teamId]);

  useEffect(() => {
    if (paramData?.roleId) {
      setRoles([paramData.roleId]);
    }
  }, [paramData?.roleId]);

  let rolesData: RoleVO[] = [];
  const { data: singleRoleVO } = trpcQuery.role.info.useQuery(
    { spaceId, id: chosenRoleId },
    {
      enabled: !!chosenRoleId,
    },
  );
  if (singleRoleVO) {
    rolesData = [singleRoleVO];
  }
  const { data: roleListData } = trpcQuery.role.list.useQuery({ spaceId });
  if (roleListData?.data) {
    rolesData.push(...roleListData.data);
  }
  const { data: teams } = trpcQuery.team.children.useQuery({
    spaceId,
    id: identify === 'GUEST' ? guestRootTeam.id : rootTeam.id,
  });

  const createInvitationLink = () => {
    if (!teamId) {
      toast.error(t.invite.invite_team_placeholder);
      return;
    }
    setLoading(true);
    createLinkInvitation.mutate(
      {
        spaceId,
        teamId,
        roleIds: roles,
        type: identify,
      },
      {
        onSuccess: () => {
          refetch();
          utils.my.reddots.invalidate();
          utils.my.todos.invalidate();
          utils.my.home.invalidate();
          setLoading(false);
          toast.success(t.invite.invite_link_created_success);
          setRoles([]);
        },
        onError: (error: unknown) => {
          setLoading(false);
          toast.error(`${t.invite.invite_link_created_fail}: ${(error as Error).message}`);
        },
      },
    );
  };

  const copyInviteURL = async (link: string) => {
    if (link) {
      await copyText(link);
      toast.success(t.invite.invite_people_by_link_copied);
    }
  };

  const deleteInvitationLink = (token: string) => {
    deleteLinkInvitation.mutate(
      { spaceId, inviteToken: token },
      {
        onSuccess: () => {
          refetch();
          toast.success(t.delete.delete_success);
        },
      },
    );
  };

  const deleteInviteConfirm = (token: string) => {
    Modal.show({
      type: 'error',
      title: t.delete.delete,
      content: t.delete.confirm_to_delete_this_link,
      okText: t.confirm,
      cancelText: t.cancel,
      onOk: () => deleteInvitationLink(token),
    });
  };

  const [options, setOptions] = React.useState<OptionProps[]>([]);

  useEffect(() => {
    setOptions(
      teams?.map((team) => ({
        label: team.name,
        value: team.id,
        hasChildren: true,
      })) || [],
    );
  }, [invitations, teams]);

  const updateOptions = (
    cloneOptions: OptionProps[],
    curTeamId: string,
    chunkOptions?: OptionProps[],
  ) => {
    let hasSet = false;
    for (const option of cloneOptions) {
      if (option.value === curTeamId) {
        hasSet = true;
        if (chunkOptions?.length === 0) {
          set(option, 'hasChildren', false);
        } else {
          set(option, 'hasChildren', true);
          set(option, 'children', chunkOptions);
        }
        const index = cloneOptions.findIndex((item) => item.value === curTeamId);
        cloneOptions[index] = option;
      }
    }
    if (!hasSet) {
      for (const option of cloneOptions) {
        if (option.hasChildren) {
          updateOptions(option.children || [], curTeamId, chunkOptions);
        }
      }
    }
  };

  const loadTeams = async (curTeamId: string) => {
    const chunkData = await trpc.team.children.query({
      spaceId,
      id: curTeamId,
    });
    const chunkOptions = chunkData.map((item) => ({
      label: item.name,
      value: item.id,
      hasChildren: true,
    }));
    const cloneOptions = JSON.parse(JSON.stringify(options));
    updateOptions(cloneOptions, curTeamId, chunkOptions);
    setOptions(cloneOptions);
  };

  if (isLoading) {
    return (
      <>
        <NavHeader>{t.invite.invite_members}</NavHeader>
        <Skeleton pos="SPACE_SETTING_PANEL" />
      </>
    );
  }

  return (
    <div className={'h-full flex flex-col pl-[24px]'}>
      <NavHeader onClose={() => (props.onClose ? props.onClose() : spaceContext.showUIModal(null))}>
        {props.title || t.invite.invite_members}
      </NavHeader>
      <Stack
        spacing={2}
        flex={1}
        sx={{
          overflowY: 'auto',
        }}
      >
        <Tabs defaultValue={visibleTabs[0]} sx={{ height: '100%' }}>
          <TabList>
            {visibleTabs.includes('LINK_INVITE') && (
              <Tab value="LINK_INVITE" disableIndicator={false}>
                {t.invite.invite_people_by_link}
              </Tab>
            )}
            {visibleTabs.includes('EMAIL_INVITE') && (
              <Tab value="EMAIL_INVITE" disableIndicator={false}>
                {t.invite.invite_people_by_email}
              </Tab>
            )}
          </TabList>
          <TabPanel value="LINK_INVITE" sx={{ px: 0 }}>
            <Typography textColor={'var(--text-primary)'} level="h7" sx={{ mb: 1 }}>
              {t.invite.create_public_invitation_link}
            </Typography>
            <RewardItem title={t.invite.create_invite_description} recordCoin={1000} />
            <Stack
              direction="row"
              justifyContent="space-between"
              spacing={2}
              alignItems="flex-end"
              sx={{ mt: 1, mb: 3 }}
            >
              <Box sx={{ flexBasis: '170px', flexShrink: '-1' }}>
                <Typography level="body-md" sx={{ pb: 1 }}>
                  {t.invite.invite_identify}
                </Typography>
                <DoubleSelect<string>
                  defaultValue={identify}
                  onChange={(value) => {
                    setIdentify(value as InvitationType);
                  }}
                  options={[
                    [
                      {
                        value: 'MEMBER',
                        label: t.invite.invite_identify_member,
                        desc: t.invite.invite_identify_member_desc,
                      },
                      {
                        value: 'GUEST',
                        label: t.invite.invite_identify_guest,
                        desc: t.invite.invite_identify_guest_desc,
                      },
                    ],
                  ]}
                />
              </Box>
              <Box sx={{ flex: 1, alignSelf: 'flex-start' }}>
                <Typography level="body-md" sx={{ pb: 1 }}>
                  {t.invite.invite_team}
                </Typography>
                <SingleSelect
                  placeholder={t.invite.invite_team_placeholder}
                  value={teamId}
                  loadData={loadTeams}
                  onChange={(value) => setTeamId(value)}
                  sx={
                    {
                      // maxWidth: '310px',
                    }
                  }
                  options={[
                    {
                      label:
                        identify === 'GUEST'
                          ? guestRootTeam.name
                          : rootTeam.name || t.space.unnamed,
                      value: identify === 'GUEST' ? guestRootTeam.id : rootTeam.id,
                      hasChildren: false,
                    },
                    ...options,
                  ]}
                />
              </Box>
              {identify === 'MEMBER' && (
                <Box sx={{ flex: 1 }}>
                  <Typography level="body-md" sx={{ pb: 1 }}>
                    {t.invite.invite_role}
                  </Typography>
                  <MultiSelect
                    options={rolesData?.map((role) => ({
                      label: role.name,
                      value: role.id,
                    }))}
                    value={roles}
                    sx={{
                      maxWidth: '310px',
                    }}
                    onChange={(selectedRoles) => {
                      setRoles(selectedRoles);
                    }}
                    placeholder={t.invite.invite_role_placeholder}
                  />
                </Box>
              )}

              <Button
                disabled={!permission?.invite}
                loading={loading}
                onClick={createInvitationLink}
                sx={{
                  borderRadius: '4px',
                  flexShrink: 0,
                  height: '40px',
                }}
              >
                {loading ? t.invite.create_invite_loading : t.buttons.create}
              </Button>
            </Stack>
            {invitations.length > 0 && (
              <>
                <Typography level="h7" sx={{ mb: 1 }}>
                  {t.invite.created_public_invitation_link}
                </Typography>
                <RewardItem
                  title={t.invite.invite_description}
                  suffix={t.invite.invite_description_next}
                  recordCoin={1000}
                />
                <RewardItem
                  title={t.invite.invite_link_have_coins}
                  recordCoin={userCoins?.balance ?? 0}
                />
                <List>
                  {invitations.map((item) => {
                    const invitationLink = `${window.location.origin}/space/join/${item.token}?referralCode=${userReferralCode}`;
                    const inviteOwner = item.member;
                    return (
                      <ListItem
                        key={item.token}
                        sx={{ pb: 2, px: 0, flexDirection: 'column', alignItems: 'normal' }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Chip
                            variant="solid"
                            sx={{
                              color: 'var(--text-primary)',
                            }}
                            startDecorator={
                              <div>
                                <AvatarImg
                                  customSize={AvatarSize.Size16}
                                  name={inviteOwner.name}
                                  avatar={inviteOwner.avatar!}
                                />
                              </div>
                            }
                          >
                            <EllipsisText>
                              <Typography
                                level={'b4'}
                                sx={{
                                  marginLeft: '4px',
                                }}
                              >
                                {inviteOwner?.name ?? t.user.no_name}
                              </Typography>
                            </EllipsisText>
                          </Chip>
                          <Typography level="b3" sx={{ mb: 0.5 }} textColor="var(--text-secondary)">
                            {t.invite.invite_link_created_by}(
                            {item.type === 'GUEST' ? t.global.guest : t.space.members} -{' '}
                            {item.team.parentId ? item.team.name : spaceName}
                            {item.roles?.length
                              ? `- ${item.roles.map((role) => role.name).join(',')}`
                              : ''}
                            )
                          </Typography>
                        </Stack>
                        <div className="flex">
                          <Input
                            defaultValue={invitationLink}
                            readOnly
                            sx={{
                              flex: 1,
                              borderRadius: '4px',
                              marginRight: '16px',
                              cursor: 'pointer',
                            }}
                          />
                          <ButtonGroup
                            sx={{
                              '--ButtonGroup-separatorSize': '0',
                              backgroundColor: 'var(--bg-controls)',
                              position: 'relative',
                              '&:before': {
                                content: '""',
                                position: 'absolute',
                                top: '8px',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                width: '1px',
                                height: '24px',
                                backgroundColor: 'var(--border-default)',
                              },
                              '& button': {
                                border: 'none',
                                width: '40px',
                                height: '40px',
                              },
                            }}
                          >
                            <Tooltip title={t.invite.copy_link} arrow placement="top">
                              <IconButton onClick={() => copyInviteURL(invitationLink)}>
                                <CopyOutlined color="var(--text-secondary)" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={t.invite.delete_link} arrow placement="top">
                              <IconButton onClick={() => deleteInviteConfirm(item.token)}>
                                <DeleteOutlined color="var(--text-secondary)" />
                              </IconButton>
                            </Tooltip>
                          </ButtonGroup>
                        </div>
                      </ListItem>
                    );
                  })}
                </List>
              </>
            )}
          </TabPanel>
          <TabPanel value="EMAIL_INVITE" sx={{ height: '100%', px: 0 }}>
            {/* <Stack
              direction="column"
              alignItems="center"
              justifyContent="center"
              flex={1}
              sx={{
                mt: 8,
              }}
              spacing={2}
            >
              <Image
                src="/assets/placeholders/email-invitation.png"
                width={160}
                height={160}
                alt="email invitation placeholder"
              />
              <Typography level="b4">{t.coming_soon}</Typography>
            </Stack> */}
            <InviteUserByEmail
              onClose={() => (props.onClose ? props.onClose() : spaceContext.showUIModal(null))}
            />
          </TabPanel>
        </Tabs>
      </Stack>
    </div>
  );
}
