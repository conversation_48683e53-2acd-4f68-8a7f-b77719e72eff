'use client';

import { useApiCaller } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import { BikaWebsiteLeftSidebar } from '@bika/domains/website/client/website-left-sidebar';
import { useShareContext, useSpace<PERSON>ontext<PERSON><PERSON>ce, useSpaceRouter } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import { IconButton } from '@bika/ui/button';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import EmailFilled from '@bika/ui/icons/components/email_filled';
import FormFilled from '@bika/ui/icons/components/form_filled';
import PinOutlined from '@bika/ui/icons/components/pin_outlined';
import ShareOutlined from '@bika/ui/icons/components/share_outlined';
import DiscordFilled from '@bika/ui/icons/doc_hide_components/discord_filled';
import FolderPersonalOutlined from '@bika/ui/icons/doc_hide_components/folder_personal_outlined';
import FolderSpaceOutlined from '@bika/ui/icons/doc_hide_components/folder_space_outlined';
import { Stack } from '@bika/ui/layouts';
import { useLocalStorageState } from 'ahooks';
import Image from 'next/image';
import type React from 'react';
import { useEffect, useState } from 'react';
import {
  type ResourceTabType,
  SPACE_RESOURCE_SIDEBAR_TAB,
  useSpaceLocalStorageState,
} from '../../../../shared/client/hooks/use-space-local-storage-state';
import { HelpVideoIframe } from '../../../../website/client/help-video/help-video-iframe';
import { SidebarTabHeader } from '../../sidebar-tree/sidebar-tab-header';
import style from '../index.module.css';
import { ShareSideBar } from '../sharing/share-side-bar';
import { showGuideVideo } from '../show-guide-video';
import { TopTabButton } from '../sidebar-top/top-tab-button';
import { PrivateResource } from './private-resource';
import { Shortcuts } from './shortcuts';
import { SpaceNodesAreaView } from './space-nodes-area-view';

const getIconColor = (isActive: boolean) => (isActive ? 'var(--static)' : 'var(--text-secondary)');

const containerStyle = 'flex-1 overflow-y-scroll w-[calc(100%_+_27px)] pr-5';

export function SpaceResourceTab() {
  const globalContext = useGlobalContext();
  const { sharing } = useShareContext();
  const locale = useLocale();
  const spaceContext = useSpaceContextForce();
  const spaceId = spaceContext?.data?.id;
  const { useRootNode } = spaceContext;
  const { findNode, rootNode } = useRootNode();
  const { lang, t } = locale;
  const [helpVideoClose, setHelpVideoClose] = useLocalStorageState<boolean>('HELP_VIDEO_CLOSE');

  const [tab, setTab] = useSpaceLocalStorageState<ResourceTabType>(
    SPACE_RESOURCE_SIDEBAR_TAB,
    'space',
  );
  const [showSpaceNodes, setShowSpaceNodes] = useState<boolean>(false);
  const { useParams } = useSpaceRouter();
  const { trpc } = useApiCaller();

  const { rootNode: privateRootNode } = useRootNode('PRIVATE');

  const { nodeId } = useParams<{ nodeId?: string }>();

  useEffect(() => {
    if (!nodeId) return;

    if (tab === 'space' || tab === 'personal') return;

    const node = findNode(nodeId);

    if (!node) {
      // 节点的深度超过 2 层，可能找不到 node
      // 跳转到 space 后会自动下载数据
      setTab('space');
      return;
    }

    setTab(node.scope === 'SPACE' ? 'space' : 'personal');
  }, [nodeId]);

  useEffect(() => {
    if (!nodeId) return;

    trpc.node.detail.query({ id: nodeId }).then((data) => {
      setTab(data.scope === 'SPACE' ? 'space' : 'personal');
    });
  }, []);

  useEffect(() => {
    // 当tab改变时，如果是space，延迟2秒加载
    if (tab === 'space') {
      setShowSpaceNodes(false); // 先重置状态
      const timer = setTimeout(() => {
        setShowSpaceNodes(true);
      }, 200);
      return () => clearTimeout(timer);
    }
    setShowSpaceNodes(false);
  }, [tab]);

  useEffect(() => {
    const tabElements = {
      smart: document.getElementById('SIDEBAR_TAB_SMART'),
      space: document.getElementById('SIDEBAR_TAB_SPACE'),
    };

    const handlers = {
      smart: () => setTab('smart'),
      space: () => setTab('space'),
    };

    // 添加事件监听器
    Object.entries(tabElements).forEach(([key, element]) => {
      if (element) {
        element.addEventListener('click', handlers[key as keyof typeof handlers]);
      }
    });

    // 清理事件监听器
    return () => {
      Object.entries(tabElements).forEach(([key, element]) => {
        if (element) {
          element.removeEventListener('click', handlers[key as keyof typeof handlers]);
        }
      });
    };
  }, []);

  if (!spaceId || spaceId.includes('fake')) {
    return null;
  }

  const isGuest = spaceContext.myInfo?.isGuest;
  const spaceText = isGuest ? t.share.share_text : t.navbar.team;

  // 这里的 member，包括了 user + ai agents
  const usersMembersCount = spaceContext.data?.usersMembersCount || 0;

  // 渲染内容区域
  const renderTabContent = (tabKey: ResourceTabType, content: React.ReactNode) => {
    const isActive = tab === tabKey;

    return (
      <div style={{ display: isActive ? 'block' : 'none' }} className={containerStyle}>
        {content}
      </div>
    );
  };

  // 渲染帮助视频
  const renderHelpVideo = () => {
    if (helpVideoClose) return null;

    return (
      <Stack
        sx={{
          cursor: 'pointer',
          margin: '0 auto',
          position: 'relative',
        }}
        my={2}
        onClick={() => {
          globalContext.showUIModal({ name: 'HELP_VIDEO', videoType: 'PRESENTATION' });
        }}
      >
        <Image
          alt=""
          width={248}
          height={160}
          src={`/assets/images/tutorial-video/tutorial-cover-${lang}.png`}
        />
        <div className="absolute top-2 right-2 flex items-center justify-center">
          <IconButton
            onClick={(e) => {
              setHelpVideoClose(true);
              e.stopPropagation();
            }}
          >
            <CloseOutlined />
          </IconButton>
        </div>
      </Stack>
    );
  };

  // 如果是分享模式，渲染分享侧边栏
  if (sharing) {
    return (
      <div
        className={
          'h-full [&>div]:h-full [&>div]:w-full [&>div>div]:w-full bg-[--bg-surface] w-full'
        }
      >
        <BikaWebsiteLeftSidebar sideBarSlot={<ShareSideBar />} />
      </div>
    );
  }

  const getRootNode = () => {
    if (tab === 'personal') return privateRootNode;
    if (tab === 'space') return rootNode;
    return undefined;
  };

  return (
    <div className={style.sidebar}>
      <div className="px-[8px]">
        <SidebarTabHeader
          title={t.navbar.resources}
          rootNode={getRootNode()}
          questionIcon={{
            onClick: () => {
              showGuideVideo({
                t,
                items: [
                  {
                    key: 'marketing',
                    title: t.website.video.marketing,
                    content: <HelpVideoIframe videoType={'INTRODUCTION'} locale={locale} />,
                    icon: <EmailFilled size={16} currentColor />,
                  },
                  {
                    key: 'onboarding',
                    title: t.website.video.onboarding,
                    content: <HelpVideoIframe videoType={'ONBOARDING'} locale={locale} />,
                    icon: <FormFilled size={16} currentColor />,
                  },
                  {
                    key: 'product',
                    title: t.website.video.product,
                    content: <HelpVideoIframe videoType={'PRESENTATION'} locale={locale} />,
                    icon: <DiscordFilled size={16} currentColor />,
                  },
                ],
                id: 'space-sidebar-help-video', // 侧边栏帮助视频的唯一ID
              });
            },
          }}
        />
      </div>
      <div className={style.bottom}>
        <nav className="flex flex-col flex-1 px-[8px] box-border justify-between overflow-hidden">
          <div className="flex flex-col flex-1 overflow-hidden">
            <div className="flex flex-shrink-0 mb-[8px] mx-[8px] items-center justify-between h-[40px] w-content px-[4px] py-[4px] bg-[--bg-controls] rounded-[8px] ">
              {/* <TopTabButton<ResourceTabType>
                  tabInfo={{
                    id: 'smart',
                    name: t.navbar.smart,
                    icon: <SmartOutlined color={getIconColor(tab === 'smart')} />,
                  }}
                  setTab={setTab}
                  isActive={tab === 'smart'}
                /> */}
              <TopTabButton<ResourceTabType>
                tabInfo={{
                  id: 'space',
                  name: spaceText,
                  icon: isGuest ? (
                    <ShareOutlined color={getIconColor(tab === 'space')} />
                  ) : (
                    <FolderSpaceOutlined color={getIconColor(tab === 'space')} />
                  ),
                }}
                setTab={setTab}
                isActive={tab === 'space'}
              />
              {!isGuest && (
                <>
                  {usersMembersCount > 1 && (
                    <TopTabButton<ResourceTabType>
                      tabInfo={{
                        id: 'personal',
                        name: t.navbar.personal,
                        icon: <FolderPersonalOutlined color={getIconColor(tab === 'personal')} />,
                      }}
                      setTab={setTab}
                      isActive={tab === 'personal'}
                    />
                  )}
                  <TopTabButton<ResourceTabType>
                    tabInfo={{
                      id: 'shortcuts',
                      name: t.navbar.shortcuts,
                      icon: <PinOutlined color={getIconColor(tab === 'shortcuts')} />,
                    }}
                    setTab={setTab}
                    isActive={tab === 'shortcuts'}
                  />
                </>
              )}
            </div>

            {/* {renderTabContent(
                'smart',
                <>
                  <MyAreaView spaceId={spaceId} />
                  <Recent />
                  {!isGuest && (
                    <Resource
                      setTab={(tab) => {
                        setTab(tab as ResourceTabType);
                      }}
                      spaceId={spaceId}
                    />
                  )}
                  <ExplorerAreaView />
                </>,
              )} */}
            {renderTabContent('space', <SpaceNodesAreaView />)}
            {renderTabContent('personal', <PrivateResource />)}
            {renderTabContent('shortcuts', <Shortcuts />)}
          </div>

          {renderHelpVideo()}
        </nav>
      </div>
    </div>
  );
}
