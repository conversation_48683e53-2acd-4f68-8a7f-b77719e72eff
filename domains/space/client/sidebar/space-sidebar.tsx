'use client';

import { useLocale } from '@bika/contents/i18n/context';
import { NodePermissionProviderWithApi } from '@bika/domains/node/client/context/node-permission-provider-with-api';
import { cn } from '@bika/domains/shared/client/utils';
import { useShareContext, useSpace<PERSON>ontextForce, useSpaceRouter } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import { Popover, PopoverContent, PopoverTrigger } from '@bika/ui/components/popover/index';
import { Badge } from '@bika/ui/forms';
import ApiOutlined from '@bika/ui/icons/components/api_outlined';
import CommentOutlined from '@bika/ui/icons/components/comment_outlined';
import EmailFilled from '@bika/ui/icons/components/email_filled';
import FolderNormalOutlined from '@bika/ui/icons/components/folder_normal_outlined';
import FormFilled from '@bika/ui/icons/components/form_filled';
import InfoCircleOutlined from '@bika/ui/icons/components/info_circle_outlined';
import NotificationOutlined from '@bika/ui/icons/components/notification_outlined';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import SearchOutlined from '@bika/ui/icons/components/search_outlined';
import UserGroupOutlined from '@bika/ui/icons/components/user_group_outlined';
import VideoOutlined from '@bika/ui/icons/components/video_outlined';
import WebOutlined from '@bika/ui/icons/components/web_outlined';
import DiscordFilled from '@bika/ui/icons/doc_hide_components/discord_filled';
import UserExpertsOutlined from '@bika/ui/icons/doc_hide_components/user_experts_outlined';
import Tooltip from '@mui/joy/Tooltip';
import { cva, type VariantProps } from 'class-variance-authority';
import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';
import {
  SPACE_SIDEBAR_TAB,
  type SpaceSidebarTabType,
  useSpaceLocalStorageState,
} from '../../../shared/client/hooks/use-space-local-storage-state';
import { HelpVideoIframe } from '../../../website/client/help-video/help-video-iframe';
import { SpaceDrawersView } from '../drawers/space-drawers-view';
import { SpaceUIStackModal } from '../modals/space-modals-view';
import { Notification } from './bottom/user-dropdown-area/notification';
import { UserDropdownView } from './bottom/user-dropdown-area/user-dropdown-view';
import { showGuideVideo } from './show-guide-video';
import { SidebarStaticItem } from './sidebar-static-item';
import { SpaceDropdownView } from './sidebar-top/space-dropdown-view';
import { SpaceExpertTab } from './space-expert-tab/space-expert-tab';
import { SpaceResourceTab } from './space-resource-tab/space-resource-tab';
import { SpaceTalkTab } from './space-talk-tab/space-talk-tab';
import { SpaceTeamTab } from './space-team-tab/space-team-tab';

// 定义侧边栏容器的样式变体
const sidebarContainerVariants = cva(
  'flex flex-col bg-[--bg-surface] border-r border-[--border-default]',
  {
    variants: {
      labelMode: {
        TEXT: 'w-[64px]', // 64px
        TOOLTIP: 'w-[64px]', // 64px
      },
    },
    defaultVariants: {
      labelMode: 'TEXT',
    },
  },
);

// 定义标签项的样式变体
const tabItemVariants = cva(
  'rounded-[8px] flex justify-center items-center cursor-pointer !text-[11px] hover:bg-[--hover]',
  {
    variants: {
      labelMode: {
        TEXT: 'h-[44px] w-[44px]', // 56px
        TOOLTIP: 'h-[44px] w-[44px]', // 56px
      },
      isActive: {
        true: 'bg-[--selected] text-[--on-selected] rounded-[8px] hover:bg-[--selected]',
        false: 'text-[--text-secondary]',
      },
    },
    defaultVariants: {
      labelMode: 'TEXT',
      isActive: false,
    },
  },
);

export interface ISpaceSidebarProps {
  labelMode?: 'TEXT' | 'TOOLTIP'; // 是否隐藏按钮文字
}

interface TabItemProps extends VariantProps<typeof tabItemVariants> {
  onClick: () => void;
  children: React.ReactNode;
  className?: string;
}

function TabItem({ labelMode, isActive, onClick, children, className, ...props }: TabItemProps) {
  return (
    <div
      className={cn(tabItemVariants({ labelMode, isActive }), className)}
      onClick={onClick}
      {...props}
    >
      <div className="flex flex-col items-center gap-0.5">{children}</div>
    </div>
  );
}

function ButtonLabel(props: {
  children: React.ReactNode;
  mode: ISpaceSidebarProps['labelMode'];
  text: string;
  className?: string;
}) {
  if (props.mode === 'TOOLTIP') {
    return (
      <Tooltip title={props.text} placement="right">
        {React.isValidElement(props.children) ? props.children : <span>{props.children}</span>}
      </Tooltip>
    );
  }
  return (
    <>
      {props.children}
      <div className={props.className || 'text-b4'}>{props.text}</div>
    </>
  );
}

/**
 * Sidebar容器
 *
 * @returns
 */
export function SpaceSidebar(props: ISpaceSidebarProps) {
  const spaceContext = useSpaceContextForce();
  const spaceRouter = useSpaceRouter();
  const { useParams } = spaceRouter;
  const searchParams = useSearchParams();
  const router = useRouter();
  const spaceId = spaceContext?.data?.id;
  const isMember = spaceContext?.myInfo?.isGuest === false;
  const { rootTeam } = spaceContext;
  const [spaceSidebarTab, setSpaceSidebarTab] = useSpaceLocalStorageState<SpaceSidebarTabType>(
    SPACE_SIDEBAR_TAB,
    'resource',
  );

  const { sharing } = useShareContext();
  const locale = useLocale();
  const { t, lang } = locale;
  const globalContext = useGlobalContext();
  const { nodeId, teamId, memberId } = useParams<{
    teamId?: string;
    memberId?: string;
    nodeId?: string;
  }>();

  // 判断 tab 是否激活的辅助函数
  const isTabActive = (tabName: SpaceSidebarTabType) => spaceSidebarTab === tabName;

  const activeSidebar = searchParams.get('activeSidebar');

  React.useEffect(() => {
    if (activeSidebar) {
      setSpaceSidebarTab(activeSidebar as SpaceSidebarTabType);
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.delete('activeSidebar');
      router.replace(`${window.location.pathname}?${newSearchParams.toString()}`, {
        scroll: false,
      });
    }
  }, [activeSidebar, searchParams]);

  return (
    <>
      <div className="flex h-full w-full">
        {!sharing && (
          <div className={sidebarContainerVariants({ labelMode: props.labelMode })}>
            <div className="flex justify-center items-center mb-[40px] mt-[16px]">
              <SpaceDropdownView />
            </div>
            <div className="flex-1 flex flex-col items-center space-y-[12px]">
              <ButtonLabel mode={props.labelMode} text={t.resource.type.chat}>
                <TabItem
                  labelMode={props.labelMode}
                  isActive={isTabActive('talk')}
                  onClick={() => setSpaceSidebarTab('talk')}
                >
                  <CommentOutlined size={22} currentColor />
                </TabItem>
              </ButtonLabel>

              <ButtonLabel mode={props.labelMode} text={t.navbar.resources}>
                <TabItem
                  labelMode={props.labelMode}
                  isActive={isTabActive('resource')}
                  onClick={() => setSpaceSidebarTab('resource')}
                >
                  <FolderNormalOutlined size={22} currentColor />
                </TabItem>
              </ButtonLabel>

              {isMember && (
                <ButtonLabel mode={props.labelMode} text={t.navbar.team}>
                  <TabItem
                    labelMode={props.labelMode}
                    isActive={isTabActive('team')}
                    onClick={() => {
                      if (!teamId && !memberId) {
                        spaceRouter.push(`/space/${spaceId}/team/${rootTeam.id}`);
                      }
                      setSpaceSidebarTab('team');
                    }}
                  >
                    <UserGroupOutlined size={22} currentColor />
                  </TabItem>
                </ButtonLabel>
              )}

              <ButtonLabel mode={props.labelMode} text={t.navbar.expert}>
                <TabItem
                  labelMode={props.labelMode}
                  isActive={isTabActive('expert')}
                  onClick={() => setSpaceSidebarTab('expert')}
                >
                  <UserExpertsOutlined size={22} currentColor />
                </TabItem>
              </ButtonLabel>
            </div>
            <div className="pb-[16px] flex flex-col items-center gap-[12px]">
              <TabItem
                labelMode={props.labelMode}
                isActive={false}
                onClick={() => {
                  spaceContext.showUIModal({
                    type: 'ai-commander',
                  });
                }}
              >
                <ButtonLabel mode={props.labelMode} text={t.action.search} className="text-[11px]">
                  <SearchOutlined size={24} color="var(--text-secondary)" />
                </ButtonLabel>
              </TabItem>

              <Popover placement="right-start">
                <ButtonLabel mode={props.labelMode} text={t.help.help} className="text-[11px]">
                  <PopoverTrigger asChild>
                    <TabItem
                      labelMode={props.labelMode}
                      isActive={false}
                      onClick={() => {}}
                      className="flex flex-col items-center gap-1 cursor-pointer"
                    >
                      <QuestionCircleOutlined
                        size={24}
                        color="var(--text-secondary)"
                        className="text-[11px]"
                      />
                    </TabItem>
                  </PopoverTrigger>
                </ButtonLabel>
                <PopoverContent>
                  <div className="bg-[--bg-popup] border-[--border-default] border rounded-md w-80 px-2 py-2 shadow-[var(--shadow-default)]">
                    <SidebarStaticItem
                      text={t.brand.website}
                      onClick={() => {
                        window.open('/?home=1', '_blank');
                      }}
                      icon={<WebOutlined />}
                    />

                    <SidebarStaticItem
                      onClick={() => {
                        globalContext.showUIModal({ name: 'CONTACT_SERVICE' });
                      }}
                      text={t.website.contact_service}
                      icon={<CommentOutlined />}
                    />
                    <SidebarStaticItem
                      onClick={() => {
                        window.open('/help/guide/developer/openapi', '_blank');
                      }}
                      text={t.website.api_doc}
                      icon={<ApiOutlined />}
                    />
                    <SidebarStaticItem
                      onClick={() => {
                        window.open(`/${lang}/blog/what-is-bika-ai`, '_blank');
                      }}
                      text={t.help.help_center}
                      icon={<QuestionCircleOutlined />}
                    />
                    <SidebarStaticItem
                      onClick={() => {
                        // globalContext.showUIModal({ name: 'HELP_VIDEO', videoType: 'PRESENTATION' });
                        showGuideVideo({
                          t,
                          items: [
                            {
                              key: 'marketing',
                              title: t.website.video.marketing,
                              content: (
                                <HelpVideoIframe videoType={'INTRODUCTION'} locale={locale} />
                              ),
                              icon: <EmailFilled size={16} currentColor />,
                            },
                            {
                              key: 'onboarding',
                              title: t.website.video.onboarding,
                              content: <HelpVideoIframe locale={locale} videoType={'ONBOARDING'} />,
                              icon: <FormFilled size={16} currentColor />,
                            },
                            {
                              key: 'product',
                              title: t.website.video.product,
                              content: (
                                <HelpVideoIframe locale={locale} videoType={'PRESENTATION'} />
                              ),
                              icon: <DiscordFilled size={16} currentColor />,
                            },
                          ],
                          id: 'space-sidebar-help-video', // 侧边栏帮助视频的唯一ID
                        });
                      }}
                      text={t.website.help_video}
                      icon={<VideoOutlined />}
                    />

                    <SidebarStaticItem
                      onClick={() => {
                        window.open('/help/index', '_blank');
                      }}
                      text={t.brand.about_brand}
                      icon={<InfoCircleOutlined />}
                    />
                  </div>
                </PopoverContent>
              </Popover>

              <Popover placement="right-start">
                <ButtonLabel
                  mode={props.labelMode}
                  text={t.notification.notification}
                  className={'text-[11px]'}
                >
                  <PopoverTrigger asChild>
                    <TabItem labelMode={props.labelMode} isActive={false} onClick={() => {}}>
                      <Badge
                        badgeContent={
                          spaceContext.redDots?.notifications &&
                          spaceContext.redDots.notifications > 0
                            ? '' // 仅红点，无字
                            : 0
                        }
                        badgeInset={'-4px 6px'}
                        className={
                          'inline-flex items-center justify-center flex-none cursor-pointer rounded-md'
                        }
                        sx={{
                          '--Badge-minHeight': '0.5rem',
                          '--Badge-paddingX': '0.2rem',
                          width: '24px',
                          height: '24px',
                        }}
                        color="danger"
                        variant="solid"
                      >
                        <NotificationOutlined size={24} color="var(--text-secondary)" />
                      </Badge>
                    </TabItem>
                  </PopoverTrigger>
                </ButtonLabel>
                <PopoverContent>
                  <Notification />
                </PopoverContent>
              </Popover>
              <div className="flex justify-center items-center h-15">
                <UserDropdownView />
              </div>
            </div>
          </div>
        )}
        <div className="flex-1 min-w-0 bg-[--bg-surface]">
          {spaceSidebarTab === 'resource' && <SpaceResourceTab />}
          {spaceSidebarTab === 'talk' && <SpaceTalkTab />}
          {spaceSidebarTab === 'team' && <SpaceTeamTab />}
          {spaceSidebarTab === 'expert' && <SpaceExpertTab />}
        </div>
      </div>
      {nodeId ? (
        <NodePermissionProviderWithApi value={{ nodeId }}>
          <SpaceUIStackModal />
          <SpaceDrawersView spaceId={spaceId} />
        </NodePermissionProviderWithApi>
      ) : (
        <>
          <SpaceUIStackModal />
          <SpaceDrawersView spaceId={spaceId} />
        </>
      )}
    </>
  );
}
