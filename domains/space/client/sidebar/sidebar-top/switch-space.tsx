'use client';

import { useLocale } from '@bika/contents/i18n/context';
import { IconButton } from '@bika/ui/button-component';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { Badge } from '@bika/ui/form-components';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import SettingOutlined from '@bika/ui/icons/components/setting_outlined';
import { Box, Stack } from '@bika/ui/layout-components';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/text-components';
import { Container } from '@bika/ui/web-layout';
import Chip from '@mui/joy/Chip';
import { Divider } from '@mui/material';
import type { AvatarLogo } from 'basenext/avatar';
import { Bo<PERSON>, User } from 'lucide-react';
import type React from 'react';
import { SubscribeTag } from '../../../../pricing/client/subscribe-tag';

interface ISwitchSpaceProps {
  list: Array<{
    cover?: string;
    name: string;
    id: string;
    messageCount?: number;
    selected?: boolean;
    logo?: AvatarLogo;
    planName: string;
    memberCount: number;
    usersMembersCount: number;
    aiMembersCount: number;
  }>;
  name: string;
  onCreate: () => void;
  onSetting?: () => void;
  onNavigate: (_spaceId: string) => void;
  more: React.ReactNode;
}

export function SwitchSpace(props: ISwitchSpaceProps) {
  const { name, list, more } = props;
  const { t } = useLocale();
  return (
    <Stack
      direction={'column'}
      padding={'0 8px'}
      width={'320px'}
      maxHeight={'90vh'}
      sx={{
        border: `1px solid ${'var(--border-default)'}`,
        borderRadius: '8px',
        backgroundColor: 'var(--bg-popup)',
        boxShadow: 'var(--shadow-high)',
      }}
    >
      <Stack
        direction={'row'}
        padding={'12px  0px 12px 8px'}
        alignItems={'center'}
        justifyContent={'space-between'}
      >
        <Box className="flex flex-row items-center">
          <Typography
            level="h7"
            sx={{
              color: 'var(--text-primary)',
            }}
          >
            {name}
          </Typography>
          <SubscribeTag />
        </Box>

        {props.onSetting && (
          <IconButton onClick={props.onSetting}>
            <SettingOutlined color={'var(--text-primary)'} size={16} />
          </IconButton>
        )}
      </Stack>

      <Divider sx={{ margin: '0 8px 4px 8px' }} />
      <Stack overflow={'auto'}>
        {list.map((space) => (
          <Box
            key={space.id}
            display={'flex'}
            flexShrink={0}
            className={'hover:rounded-[4px] w-full overflow-x-hidden space-x-[4px] px-[8px]'}
            sx={{
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: 'var(--bg-controls)',
              },
            }}
            onClick={() => props.onNavigate?.(space.id)}
            justifyContent={'space-between'}
            alignItems={'center'}
            height={'48px'}
          >
            <Stack
              direction={'row'}
              alignItems={'center'}
              flex="1 1 auto"
              className={'overflow-x-hidden'}
            >
              {/* <Box
                width={'32px'}
                height={'32px'}
                flex={'0 0 32px'}
                sx={{
                  backgroundColor: colors.rainbowPurple5,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textTransform: 'uppercase',
                  color: 'var(--static)',
                  marginRight: '8px',
                }}
                borderRadius={'4px'}
                borderColor={'var(--border-default)'}
              >
                {space.name.slice(0, 1)}
              </Box> */}

              <Box sx={{ marginRight: '8px' }}>
                <AvatarImg
                  name={space.name || ''}
                  customSize={AvatarSize.Size32}
                  avatar={space.logo}
                  shape={'SQUARE'}
                />
              </Box>

              <Box
                display={'flex'}
                alignItems={'flex-start'}
                overflow={'auto'}
                flexDirection={'column'}
              >
                <EllipsisText>
                  <p className="text-b2 text-[--text-primary]">{space.name}</p>
                </EllipsisText>
                <Stack direction={'row'} gap={0.5} flexWrap={'wrap'}>
                  <Chip size="sm" variant="soft" color="neutral" sx={{ fontSize: 10, height: 18 }}>
                    {space.planName?.toUpperCase?.() ?? 'FREE'}
                  </Chip>
                  <Chip
                    size="sm"
                    variant="soft"
                    color="primary"
                    startDecorator={<User size={12} />}
                    sx={{ fontSize: 10, height: 18 }}
                  >
                    {space.memberCount}
                  </Chip>
                </Stack>
              </Box>
            </Stack>

            <Box
              className={'inline-flex items-center flex justify-center'}
              sx={{
                flex: '0 0 24px',
              }}
            >
              {space.selected ? <CheckOutlined color={'var(--brand)'} size={16} /> : null}

              {!space.selected && space.messageCount && space.messageCount !== 0 ? (
                <Badge
                  badgeContent={''}
                  sx={{
                    '--Badge-minHeight': '0.5rem',
                    '--Badge-paddingX': '0.2rem',
                    ' > span': {
                      backgroundColor: '#F07369',
                      boxShadow: 'none',
                    },
                  }}
                  color="danger"
                  variant="solid"
                />
              ) : null}
            </Box>
          </Box>
        ))}
      </Stack>

      {more}

      <Divider sx={{ margin: '4px 8px ' }} />
      <Container
        width={'100%'}
        justifyContent={'center'}
        className={'hover:rounded-[4px]'}
        onClick={props.onCreate}
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: 'var(--bg-controls)',
          },
          height: '32px',
          marginBottom: '8px',
        }}
      >
        <Box
          className={'flex inline-flex items-center'}
          sx={{
            display: 'flex',
            alignItems: 'center',
            height: '20px',
          }}
        >
          <AddOutlined color={'var(--text-secondary)'} size={16} />
        </Box>
        <Typography
          level={'b3'}
          sx={{
            marginLeft: '8px',
            color: 'var(--text-primary)',
          }}
        >
          {t.space.new_space}
        </Typography>
      </Container>
    </Stack>
  );
}
