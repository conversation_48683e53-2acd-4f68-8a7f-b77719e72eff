'use client';

import { AnimatePresence, motion } from 'motion/react';
import type React from 'react';
import { utils } from '../../../../shared/client';

interface ITabInfo<TAB extends string> {
  id: TAB;
  name: string;
  icon: React.ReactNode;
}

interface Props<TAB extends string> {
  setTab: (tab: TAB) => void;
  isActive: boolean;
  tabInfo: ITabInfo<TAB>;
}

export function TopTabButton<TAB extends string>({ setTab, isActive, tabInfo }: Props<TAB>) {
  return (
    <motion.div
      key={tabInfo.id}
      className="relative h-full flex items-center cursor-pointer  justify-center flex-1 "
      layout
      transition={{
        ease: 'linear',
        duration: 0.1,
      }}
    >
      {isActive && (
        <motion.div
          className="absolute w-full h-full bg-[--bg-elevated] rounded-[6px]"
          layoutId="activeTabBackground"
          initial={false}
          transition={{
            ease: 'linear',
            duration: 0.2,
          }}
          style={{
            zIndex: 0,
          }}
        />
      )}

      <motion.div
        // ref={(el) => (tabRefs.current[index] = el)}
        // className={`flex items-center h-full px-2 space-x-1 relative z-10 ${
        //   isActive ? 'text-white' : 'text-gray-400 hover:text-gray-300'
        // }`}
        className={utils.cn(
          'flex items-center h-full px-2 space-x-1 relative z-10',
          isActive ? 'text-[--on-selected]' : 'text-[--text-secondary] hover:text-[--text-primary]',
        )}
        onClick={() => setTab(tabInfo.id as TAB)}
      >
        {/* <div
          className={utils.cn(
            'flex items-center justify-center relative top-[-1px]',
            !isActive && 'hover:bg-[var(--hover)] p-2 rounded-full',
          )}
        >
          {tabInfo.icon}
        </div> */}

        <AnimatePresence initial={false}>
          <motion.span
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 'min-content', opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{
              ease: 'linear',
              duration: 0.15,
            }}
            className="whitespace-nowrap  text-[13px] overflow-hidden"
          >
            <motion.span
              className="inline-block"
              initial="hidden"
              animate="visible"
              exit="exit"
              variants={{
                hidden: {},
                visible: {},
                exit: {},
              }}
              transition={{
                staggerChildren: 0.01,
                staggerDirection: -1,
              }}
            >
              {tabInfo.name}
            </motion.span>
          </motion.span>
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
}
