import { useLocale } from '@bika/contents/i18n';
import UserOutlined from '@bika/ui/icons/components/user_outlined';
import UserGuestOutlined from '@bika/ui/icons/doc_hide_components/user_guest_outlined';
import UserRoleOutlined from '@bika/ui/icons/doc_hide_components/user_role_outlined';
import {
  SPACE_TEAM_SIDEBAR_TAB,
  type UnitTabType,
  useSpaceLocalStorageState,
} from '../../../../shared/client/hooks/use-space-local-storage-state';
import { SidebarTabHeader } from '../../sidebar-tree/sidebar-tab-header';
import { TopTabButton } from '../sidebar-top/top-tab-button';
import { SpaceTeamTabGuests } from './space-team-tab-guests';
import { SpaceTeamTabMembers } from './space-team-tab-members';
import { SpaceTeamTabRoles } from './space-team-tab-roles';

const getIconColor = (isActive: boolean) => (isActive ? 'var(--static)' : 'var(--text-secondary)');

export function SpaceTeamTab() {
  const { t } = useLocale();
  const [tab, setTab] = useSpaceLocalStorageState<UnitTabType>(SPACE_TEAM_SIDEBAR_TAB, 'members');

  return (
    <div className="mx-[8px] h-full flex flex-col">
      <SidebarTabHeader title={t.navbar.team} />
      {/* <ExplorerAreaView /> */}
      <div className="flex flex-shrink-0 mb-[8px] mx-[8px] items-center justify-between h-[40px] w-content px-[4px] py-[4px] bg-[--bg-controls] rounded-[8px] ">
        <TopTabButton<UnitTabType>
          tabInfo={{
            id: 'members',
            name: t.space.members,
            icon: <UserOutlined color={getIconColor(tab === 'members')} />,
          }}
          setTab={setTab}
          isActive={tab === 'members'}
        />
        <TopTabButton<UnitTabType>
          tabInfo={{
            id: 'roles',
            name: t.role.role,
            icon: <UserRoleOutlined color={getIconColor(tab === 'roles')} />,
          }}
          setTab={setTab}
          isActive={tab === 'roles'}
        />
        <TopTabButton<UnitTabType>
          tabInfo={{
            id: 'guests',
            name: t.global.guest,
            icon: <UserGuestOutlined color={getIconColor(tab === 'guests')} />,
          }}
          setTab={setTab}
          isActive={tab === 'guests'}
        />
      </div>
      <div className="px-[8px] h-full">
        {tab === 'members' && <SpaceTeamTabMembers />}
        {tab === 'roles' && <SpaceTeamTabRoles />}
        {tab === 'guests' && <SpaceTeamTabGuests />}
      </div>
    </div>
  );
}
