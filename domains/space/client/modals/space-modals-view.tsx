import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { NodeInfoModalView } from '@bika/domains/node/client/modals/node-info-modal-view';
import { NodeResourceModalView } from '@bika/domains/node/client/modals/node-resource-modal-view';
import { DeleteSpaceModal } from '@bika/domains/space/client/space-settings/delete-space-modal';
import type { AIIntentParams } from '@bika/types/ai/bo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { SurveyInput } from '@bika/ui/admin/types-form/survey-input';
import { ModalComponent } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { SnackbarProvider } from '@bika/ui/snackbar';
import { StackHeaderBarConfig, StackHeaderBarProvider } from '@bika/ui/stack-header-bar';
import { useKeyPress } from 'ahooks';
import dynamic from 'next/dynamic';
import type React from 'react';
import { useEffect, useState } from 'react';
import { ModalPageContextProviderWithStates } from '../../../database/client/record-detail/modal-as-page-provider';
import { AICreditLimitModal } from '../ai-credit-limit-modal';
import { SpaceModalConfig, useSpaceModalContext } from './space-modals-context';

// const EditMissionStack = dynamic(() =>
//   import('@bika/domains/story-components/mission/all-in-one/edit-mission-stack-component').then(
//     (res) => res.EditMissionStack,
//   ),
// );

const CreateAnythingModalView = dynamic(
  () =>
    import('../../../ai/client/launcher/views/create-anything-view').then(
      (res) => res.CreateAnythingModal,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const PricingModalView = dynamic(
  () =>
    import('@bika/domains/space/client/space-settings/space-settings-panel-upgrade').then(
      (res) => res.SpaceSettingsPanelUpgrade,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const AICommanderModalView = dynamic(
  () =>
    import('../../../ai/client/launcher/views/ai-commanders-view').then(
      (res) => res.AICommanderView,
    ),
  {
    loading: () => <Skeleton pos="AI_LAUNCHER" />,
  },
);
// 独立Modal
const AIWizardView = dynamic(
  () =>
    import('../../../ai/client/wizard/ai-wizard-view-deprecated').then(
      (res) => res.AIWizardViewDepreciated,
    ),
  {
    loading: () => <Skeleton pos="AI_LAUNCHER" />,
  },
);

const ResourceEditorStack = dynamic(
  () =>
    import('../../../editor/client/resource-editor/resource-editor-stack').then(
      (res) => res.ResourceEditorStack,
    ),
  {
    loading: () => <Skeleton pos="EDITOR_HOME" />,
  },
);
const MissionDetailModalView = dynamic(
  () =>
    import('@bika/domains/mission/client/mission-detail-modal').then(
      (res) => res.MissionDetailModal,
    ),
  { loading: () => <Skeleton pos="MISSION_DETAIL" /> },
);
const ReportDetailModalView = dynamic(
  () =>
    import('@bika/domains/report/client/report-detail-modal').then((res) => res.ReportDetailModal),
  {
    loading: () => <Skeleton pos="REPORT_DETAIL" />,
  },
);
const TemplateDetailModalView = dynamic(
  () =>
    import('@bika/domains/template/client/template-detail-modal').then(
      (res) => res.TemplateDetailModal,
    ),
  {
    loading: () => <Skeleton pos="TEMPLATE_DETAIL" />,
  },
);

const InviteMembersView = dynamic(
  () => import('../space-settings/invite/space-invite-view').then((res) => res.SpaceInviteView),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);
const SpaceSettingsView = dynamic(
  () => import('../space-settings/space-settings-view').then((res) => res.SpaceSettingsTabsView),
  {
    loading: () => <Skeleton pos="SPACE_SETTINGS" />,
  },
);
const UserNotificationView = dynamic(() =>
  import('@bika/domains/user/client/user-notification-modal').then((res) => res.default),
);
const RecordDetailStack = dynamic(
  () =>
    import('../../../database/client/record-detail/record-detail-stack').then(
      (res) => res.RecordDetailStack,
    ),
  {
    loading: () => <Skeleton pos={'RECORD_DETAIL'} />,
  },
);

const RequestRecordMissionModal = dynamic(
  () =>
    import('@bika/domains/database/client/record-request/request-record-mission-modal').then(
      (res) => res.RequestRecordMissionModal,
    ),
  { loading: () => <Skeleton pos="SPACE_MODAL" /> },
);

const IntegrationDetail = dynamic(
  () =>
    import('@bika/domains/integration/client/integration-detail').then(
      (res) => res.IntegrationDetail,
    ),
  { loading: () => <Skeleton pos="SPACE_MODAL" /> },
);
const MemberNameModal = dynamic(
  () =>
    import('@bika/domains/space/client/space-settings/member-name-setting-modal').then(
      (res) => res.default,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const NodeShareAndPermissionModal = dynamic(
  () =>
    import('@bika/domains/node/client/share-modal').then((res) => res.NodeShareAndPermissionModal),
  {
    loading: () => <Skeleton pos="SHARE" />,
  },
);

const AIShareModal = dynamic(
  () => import('@bika/domains/ai/client/ai-history/ai-share-modal').then((res) => res.AIShareModal),
  {
    loading: () => <Skeleton pos="SHARE" />,
  },
);

const WidgetModalView = dynamic(
  () =>
    import('@bika/domains/dashboard/client/widgets/widget-modal').then((res) => res.WidgetModal),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const ImportExcelModal = dynamic(
  () =>
    import('@bika/domains/database/client/import-excel/import-excel-modal').then(
      (res) => res.ImportExcelModal,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const ImportBikaFileModal = dynamic(
  () =>
    import('@bika/domains/database/client/import-bika-file/import-bika-file-modal').then(
      (res) => res.ImportBikaFileModal,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const ImportVikaModal = dynamic(
  () =>
    import('@bika/domains/database/client/import-vika/import-vika-modal').then(
      (res) => res.ImportVikaModal,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const RequestOperationPermissionModal = dynamic(
  () =>
    import('@bika/domains/database/client/import-excel/request-import-permission').then(
      (res) => res.RequestImportPermission,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const ExportExcelModal = dynamic(
  () =>
    import('@bika/domains/database/client/export-excel/export-excel-modal').then(
      (res) => res.ExportExcelModal,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const AlertReachLimitModal = dynamic(
  () => import('@bika/domains/space/client/alert-reach-limit').then((res) => res.AlertReachLimit),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

const ExportBikaFileModal = dynamic(
  () =>
    import('@bika/domains/database/client/export-bika-file/export-bika-file').then(
      (res) => res.ExportBikaFile,
    ),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);
const StoreTemplateCreateInput = dynamic(
  () => import('@bika/domains/template/client').then((res) => res.StoreTemplateCreateInput),
  {
    loading: () => <Skeleton pos="TEMPLATE_CREATE" />,
  },
);

const NodeCreateView = dynamic(
  () => import('@bika/domains/node/client/node-create-view').then((res) => res.NodeCreateView),
  {
    loading: () => <Skeleton pos="SPACE_MODAL" />,
  },
);

/**
 * 模态窗集合容器，全局只有这个Modal，这是一个堆栈，
 * 请其它地方不要自己搞Modal谢谢
 *
 * @returns
 */
function SpaceUIStackModalBase() {
  const locale = useLocale();
  const { t } = useLocale();
  const spaceContext = useSpaceContextForce();
  // const { t } = useLocale();
  const [onboardingAIIntentType, setOnboardingAIIntentType] = useState<AIIntentParams | null>();
  const spaceId = spaceContext.data.id;
  const { setUsageLimitError } = useApiCaller();

  // apple + k
  useKeyPress(['meta.k'], () => {
    // startAICommander();
    spaceContext.showUIModal({ type: 'ai-commander' });
  });

  useEffect(() => {
    // 这里考虑 设置下新手引导
    const refreshOnboardingAIIntentType = () => {
      if (spaceContext.data.settings?.onboardingStage !== 'DONE') {
        // localStorage.setItem('WIZARD_GUIDE', JSON.stringify({ id: 'USER_WIZARD_GUIDE' })); // 临时隐藏新手引导
        // setOnboardingAIIntentType({ type: 'STEP_WIZARD', stepWizardType: 'ONBOARDING_UI' });
      } else {
        // setOnboardingAIIntentType(null);
      }
      // 获取当前的onboarding阶段，以及对应的AI意图显示类型
      // switch (spaceContext.data.settings?.onboarding) {
      //   case 'UI':
      //     setOnboardingAIIntentType({ type: 'STEP_WIZARD', stepWizardType: 'ONBOARDING_UI' });
      //     break;
      //   case 'AUTH':
      //     setOnboardingAIIntentType({ type: 'STEP_WIZARD', stepWizardType: 'ONBOARDING_AUTH' });
      //     break;
      //   case 'TRIAL':
      //     setOnboardingAIIntentType({ type: 'STEP_WIZARD', stepWizardType: 'ONBOARDING_TRIAL' });
      //     break;
      //   case 'DONE':
      //     setOnboardingAIIntentType(null);
      //     break;
      //   // case 'INIT':
      //   default:
      //     setOnboardingAIIntentType({ type: 'STEP_WIZARD', stepWizardType: 'ONBOARDING_INIT' });
      // }
    };
    refreshOnboardingAIIntentType();
  }, [spaceContext.data.settings?.onboardingStage]);

  const modalObj = spaceContext.getUIModal();
  const modal = modalObj ? modalObj.type : null;

  const value = useSpaceModalContext();

  const closeModal = (_event: React.MouseEvent<HTMLButtonElement>, reason: string) => {
    if (value.disabledBackdropClick === true && reason === 'backdropClick') {
      return;
    }
    if (modalObj?.disabledBackdropClick === true && reason === 'backdropClick') {
      return;
    }
    if (modalObj?.type === 'alert-reach-limit') {
      // 如果是 alert-reach-limit, 清理缓存
      setUsageLimitError?.(null);
    }
    spaceContext.showUIModal(null);
  };

  if (modal === 'template') {
    return <TemplateDetailModalView />;
  }
  if (modalObj?.type === 'publish-template') {
    return (
      <StoreTemplateCreateInput
        onSubmit={spaceContext.refetch}
        onClose={() => {
          spaceContext.showUIModal(null);
        }}
        republish={modalObj.republish}
        spaceId={spaceId}
        nodeId={modalObj.nodeId}
        templateId={modalObj.templateId}
        value={modalObj.templateMetadata}
      />
    );
  }
  if (modal === 'ai-commander') {
    return <AICommanderModalView />;
  }

  return (
    <SnackbarProvider>
      {/* {onboardingAIIntentType && (
        <AIWizardView
          initAIIntent={onboardingAIIntentType}
          onClose={() => {
            spaceContext.refetch(); // 刷新space context，使wizard消失
          }}
          withModal={true}
        />
      )} */}

      {modal && (
        <ModalComponent
          closable={value.showClose === true}
          width={value.width}
          className={value.className}
          title={value.title}
          onClose={closeModal}
          disableEscapeKeyDown={value.disableEscapeKeyDown === true}
          zIndex={value.zIndex || 1300}
          sx={{
            p:
              modalObj?.type && ['ai-credit-limit', 'alert-reach-limit'].includes(modalObj?.type)
                ? 0
                : 2,
          }}
        >
          <StackHeaderBarProvider>
            {modal === 'notification-center' && (
              <>
                <SpaceModalConfig />
                <StackHeaderBarConfig title={t.notification.notification} />
                <UserNotificationView />
              </>
            )}
            {modalObj?.type === 'space-settings' && (
              <>
                <SpaceModalConfig width={1100} showClose={false} />
                <StackHeaderBarConfig title="" />
                {/* 内置网址记忆tab */}
                <SpaceSettingsView modalObj={modalObj} />
              </>
            )}

            {modalObj?.type === 'integration-detail' && (
              <>
                <SpaceModalConfig />
                <StackHeaderBarConfig title="" leftControls />
                <IntegrationDetail
                  type={modalObj?.integration.type}
                  data={modalObj?.integration.data}
                />
              </>
            )}

            {modalObj?.type === 'mission' && (
              <>
                <SpaceModalConfig title="" showClose={true} />
                <StackHeaderBarConfig title="" />
                <MissionDetailModalView
                  spaceId={spaceContext.data.id}
                  missionId={modalObj?.missionId}
                  isModal={true}
                />
              </>
            )}

            {modalObj?.type === 'node-info' && (
              <>
                <SpaceModalConfig title={''} width={400} />
                <StackHeaderBarConfig title="" />
                <NodeInfoModalView nodeId={modalObj.nodeId} resourceType={modalObj.resourceType} />
              </>
            )}

            {modalObj?.type === 'delete-space' && (
              <>
                <SpaceModalConfig title={''} width={400} />
                <StackHeaderBarConfig title="" />
                <DeleteSpaceModal />
              </>
            )}

            {modalObj?.type === 'node-select' && (
              <>
                <SpaceModalConfig title={''} width={560} />
                <StackHeaderBarConfig title="" />
                <NodeResourceModalView
                  value={modalObj.value}
                  onClose={() => {
                    spaceContext.showUIModal(null);
                  }}
                />
              </>
            )}

            {modalObj?.type === 'report' && (
              <>
                <SpaceModalConfig title={t.report.report_detail} />
                <StackHeaderBarConfig title="" />
                <ReportDetailModalView reportId={modalObj?.reportId} />
              </>
            )}
            {modalObj?.type === 'alert-reach-limit' && (
              <>
                <SpaceModalConfig width={1000} title="" />
                <StackHeaderBarConfig title="" />
                <AlertReachLimitModal text={modalObj.text} />
              </>
            )}

            {/* {modal === 'ai-commander' && (
              <>
                <SpaceModalConfig title="" />
                <StackHeaderBarConfig title="" />
                <AICommanderModalView />
              </>
            )} */}

            {modal === 'create-anything' && (
              <>
                <SpaceModalConfig title="" />
                <StackHeaderBarConfig title="" />
                <CreateAnythingModalView />
              </>
            )}

            {modal === 'template-editor' && (
              <>
                {/* 内部Stack设置 */}
                <ResourceEditorStack
                  screen={{
                    screenType: 'HOME',
                  }}
                  // slotComponents={{
                  //   EditMissionComponent: (props: Omit<EditMissionStackProps, 'dataProvider'>) => (
                  //     <EditMissionStack dataProvider={'SPACE'} {...props} />
                  //   ),
                  // }}
                  dataProvider="SPACE"
                  locale={locale}
                />
              </>
            )}

            {modalObj?.type === 'node-share-and-permission' && (
              <>
                <SpaceModalConfig width={600} showClose={false} />
                <StackHeaderBarConfig title={''} />
                <NodeShareAndPermissionModal nodeId={modalObj.nodeId} option={modalObj.option} />
              </>
            )}

            {modalObj?.type === 'ai-share' && (
              <>
                <SpaceModalConfig width={600} showClose={false} />
                <StackHeaderBarConfig title={''} />
                <AIShareModal wizardId={modalObj.wizardId} />
              </>
            )}

            {modalObj?.type === 'invite-members' && (
              <>
                <SpaceModalConfig width={800} showClose={false} />
                <StackHeaderBarConfig title="" />
                <InviteMembersView teamId={modalObj?.teamId} roleId={modalObj?.roleId} />
              </>
            )}

            {modalObj?.type === 'create-record' && (
              <>
                <SpaceModalConfig title={t.record.create_record} />
                <StackHeaderBarConfig title="" />
                <RecordDetailStack
                  viewId={modalObj?.viewId}
                  databaseId={modalObj?.databaseId}
                  mirrorId={modalObj?.mirrorId}
                  initialStatus={'CREATE'}
                />
              </>
            )}

            {modalObj?.type === 'update-record' && (
              <>
                <SpaceModalConfig />
                <StackHeaderBarConfig title={t.record.record_detail} />

                {/* // TODO check initail status */}
                <RecordDetailStack
                  databaseId={modalObj?.databaseId}
                  viewId={modalObj?.viewId}
                  recordId={modalObj?.recordId}
                  initialStatus={'UPDATE'}
                />
              </>
            )}

            {modalObj?.type === 'record-detail' && (
              <>
                <SpaceModalConfig />
                <StackHeaderBarConfig title={t.record.record_detail} />
                <RecordDetailStack
                  key={modalObj?.recordId}
                  recordId={modalObj?.recordId}
                  databaseId={modalObj?.databaseId}
                  viewId={modalObj?.viewId}
                  mirrorId={modalObj?.mirrorId}
                  initialStatus="VIEW"
                  commentExpanded={modalObj?.commentExpanded}
                />
              </>
            )}

            {modalObj?.type === 'request-record-mission' && (
              <>
                <SpaceModalConfig />
                <StackHeaderBarConfig title={t.record.request_new_record} />
                <RequestRecordMissionModal
                  databaseId={modalObj?.databaseId}
                  mirrorId={modalObj?.mirrorId}
                  viewId={modalObj?.viewId}
                />
              </>
            )}
            {modalObj?.type === 'import-excel' && (
              <>
                <SpaceModalConfig showClose={false} />
                <StackHeaderBarConfig title={''} />
                <ImportExcelModal
                  databaseId={modalObj?.databaseId}
                  importEnabled={modalObj?.importEnabled}
                  folderId={modalObj?.folderId}
                />
              </>
            )}
            {modalObj?.type === 'import-vika' && (
              <>
                <SpaceModalConfig showClose={false} />
                <StackHeaderBarConfig title={''} />
                <ImportVikaModal folderId={modalObj?.folderId} />
              </>
            )}
            {modalObj?.type === 'import-bika-file' && (
              <>
                <SpaceModalConfig showClose={false} />
                <StackHeaderBarConfig title={''} />
                <ImportBikaFileModal
                  importEnabled={modalObj?.importEnabled}
                  folderId={modalObj?.folderId}
                />
              </>
            )}
            {modalObj?.type === 'export-excel' && (
              <>
                <SpaceModalConfig showClose={false} width={'560px'} />
                <StackHeaderBarConfig title={''} />
                <ExportExcelModal nodeId={modalObj?.nodeId} />
              </>
            )}
            {modalObj?.type === 'export-attachments' && (
              <>
                <SpaceModalConfig showClose={false} width={'560px'} />
                <StackHeaderBarConfig title={''} />
                <SurveyInput surveyType={'COMING_SOON_FEATURE'} />
              </>
            )}
            {modalObj?.type === 'export-bika-file' && (
              <>
                <SpaceModalConfig showClose={false} width={'560px'} />
                <StackHeaderBarConfig title={''} />
                <ExportBikaFileModal
                  nodeId={modalObj?.nodeId}
                  exportType={modalObj?.exportType}
                  includeData={modalObj?.includeData}
                />
              </>
            )}
            {modalObj?.type === 'request-operation-permission' && (
              <>
                <SpaceModalConfig showClose={false} />
                <StackHeaderBarConfig title={''} />
                <RequestOperationPermissionModal nodeId={''} />
              </>
            )}

            {modalObj?.type === 'member-name-setting' && (
              <>
                <SpaceModalConfig
                  showClose={true}
                  width="480px"
                  title={t.settings.member.set_space_member_name}
                />
                <StackHeaderBarConfig title="" />
                <MemberNameModal name={modalObj?.name} />
              </>
            )}

            {modalObj?.type === 'dashboard-widget' && (
              <>
                <SpaceModalConfig
                  title=""
                  showClose={false}
                  zIndex={1299}
                  className="!p-0 !border-0"
                />
                <StackHeaderBarConfig title="" />
                <WidgetModalView
                  locale={locale}
                  widgetId={modalObj.widgetId}
                  privilege={modalObj?.privilege}
                  dashboardId={modalObj.dashboardId}
                />
              </>
            )}
            {modalObj?.type === 'create-node' && (
              <>
                <SpaceModalConfig width={1000} title={t.resource.type.create_node_resource} />
                <StackHeaderBarConfig title="" />
                <NodeCreateView {...modalObj.props} />
              </>
            )}
            {modalObj?.type === 'pricing' && (
              <>
                <SpaceModalConfig title={''} showClose={false} />
                <StackHeaderBarConfig title="" />
                <PricingModalView />
              </>
            )}
            {modalObj?.type === 'ai-credit-limit' && (
              <>
                <SpaceModalConfig title={''} showClose />
                <StackHeaderBarConfig title="" />
                <AICreditLimitModal />
              </>
            )}
          </StackHeaderBarProvider>
        </ModalComponent>
      )}
    </SnackbarProvider>
  );
}

export function SpaceUIStackModal() {
  return (
    <ModalPageContextProviderWithStates>
      <SpaceUIStackModalBase />
    </ModalPageContextProviderWithStates>
  );
}
