import { errors, ServerError } from '@bika/contents/config/server/error';
import { AIChatSO } from '@bika/domains/ai/server/ai-chat/ai-chat-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { ShareResourceType, ShareScope, type ShortURLRelationType } from '@bika/server-orm';
import type { AIChatShareScope } from '@bika/types/ai/bo';
import type { ShareUpsertDTO } from '@bika/types/node/dto';
import type { ShareResourceVO, ShareVO } from '@bika/types/node/vo';
import _ from 'lodash';
import { AISearchSO } from '../../ai/server/ai-search-so';
import { ShareSO } from '../../permission/server/share-so';
import { NodeSO } from '../server/node-so';
import { ShortURLSO } from '../server/short-url-so';

export class ShareController {
  static async upsert(user: UserSO, input: ShareUpsertDTO) {
    const { spaceId, data } = input;
    // check permission
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'publishResource');
    const share = await ShareSO.upsert(user.id, data);
    if (data.resourceType === 'AI_CHAT') {
      await AIChatSO.setShare(share.resourceId, {
        scope:
          (data.scope as AIChatShareScope) === 'PUBLIC_READ' && data.settings?.toCommunity
            ? 'PUBLISH'
            : (data.scope as AIChatShareScope),
        id: share.id,
      });
      AISearchSO.rewriteAIChatIndex(share.resourceId);
    }
    return share;
  }

  static async retrieveInfo(
    shareId: string,
    userId?: string,
  ): Promise<{ share: ShareVO; resource: ShareResourceVO }> {
    const share = await ShareSO.init(shareId);
    if (!share) {
      throw new ServerError(errors.share.share_link_invalid);
    }
    if (share.needAuth && !userId) {
      console.log('need auth', share.scope, share.needAuth, userId);
      throw new ServerError(errors.share.need_auth);
    }

    const user = userId ? await UserSO.init(userId) : undefined;

    if (share.resourceType === ShareResourceType.NODE) {
      return ShareController.getNodeShareInfo(share, user);
    }
    if (share.resourceType === ShareResourceType.AI_CHAT) {
      return ShareController.getAIChatShareInfo(share, user);
    }
    throw new ServerError(errors.share.share_link_invalid);
  }

  static async toggleShortURL(relationId: string, relationType: ShortURLRelationType) {
    const shortURL = await ShortURLSO.get(relationType, relationId);
    if (!shortURL) {
      const newShortURL = await ShortURLSO.create(relationType, relationId);
      return newShortURL.toVO();
    } else {
      await ShortURLSO.delete(shortURL.model.id);
      return null;
    }
  }

  private static async getNodeShareInfo(
    share: ShareSO,
    user?: UserSO,
  ): Promise<{ resource: ShareResourceVO; share: ShareVO }> {
    const node = await NodeSO.init(share.resourceId);
    const space = await node.getSpace();
    return {
      resource: {
        type: ShareResourceType.NODE,
        node: await node.toNodeDetailVO({ locale: user?.locale, userId: user?.id }),
        space: space.toSimpleVO(),
      },
      share: share.toVO(),
    };
  }

  private static async getAIChatShareInfo(
    share: ShareSO,
    user?: UserSO,
  ): Promise<{ resource: ShareResourceVO; share: ShareVO }> {
    if (share.scope === ShareScope.DEFAULT && !user) {
      throw new ServerError(errors.share.share_link_invalid);
    }
    const aiChat = await AIChatSO.init(share.resourceId);
    const space = await aiChat.getSpace();
    if (!space) {
      throw new ServerError(errors.share.share_link_invalid);
    }
    const intent = aiChat.intent.toVO();
    if (
      intent.type === 'AI_NODE' ||
      (intent.type === 'COPILOT' && intent.copilot?.type === 'node')
    ) {
      const nodeId = intent.type === 'AI_NODE' ? intent.nodeId : _.get(intent.copilot, 'nodeId');
      if (!nodeId) {
        throw new ServerError(errors.share.share_link_invalid);
      }
      const node = await NodeSO.init(nodeId);
      return {
        resource: {
          type: ShareResourceType.AI_CHAT,
          agent: {
            type: 'node',
            node: await node.toNodeDetailVO({ locale: user?.locale }),
          },
          space: space.toSimpleVO(),
        },
        share: share.toVO(),
      };
    }
    if (intent.type === 'BUILDER' || intent.type === 'SUPERVISOR') {
      return {
        resource: {
          type: ShareResourceType.AI_CHAT,
          agent: {
            type: 'expert',
            expertKey: intent.type === 'BUILDER' ? 'builder' : 'supervisor',
          },
          space: space.toSimpleVO(),
        },
        share: share.toVO(),
      };
    }
    throw new ServerError(errors.share.share_link_invalid);
  }
}
