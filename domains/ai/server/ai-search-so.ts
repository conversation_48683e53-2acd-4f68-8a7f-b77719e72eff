import { getDefaultAIModel } from '@bika/contents/config/server/ai/ai-model-config';
import { PromptAiSearch } from '@bika/contents/config/server/ai-prompts/prompt-ai-search';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import type { RecordSO } from '@bika/domains/database/server/record-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { wait } from '@bika/domains/shared/server';
import type { SpaceSO } from '@bika/domains/space/server/space-so';
import type { UserSO } from '@bika/domains/user/server/user-so';
import {
  AIPublishedChatSearchIndexSchema,
  db,
  type Node as NodePO,
  WebPageSearchIndexSchema,
} from '@bika/server-orm';
import type { LauncherCommand, PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import type { PublishedChatPaginationDTO } from '@bika/types/ai/dto';
import type { AIChatPaginationVO, AIChatSimpleVO, AIMessageVO } from '@bika/types/ai/vo';
import type { DocumentBO } from '@bika/types/document/bo';
import type { NodeResourceType } from '@bika/types/node/bo';
import type { estypes } from '@elastic/elasticsearch';
import type { StreamTextResult, ToolSet } from 'ai';
import { type iString, iStringParse } from 'basenext/i18n';
import _ from 'lodash';
import type { ISpaceSO } from 'sharelib/space';
import { canReadNode } from '../../node/server/utils';
import { AIChatSO } from './ai-chat';

type QueryDslQueryContainer = estypes.QueryDslQueryContainer;
/**
 * 处理节点的搜索索引
 */
export class AISearchSO {
  /**
   * 遍历Elastic Search，对照看OLTP里有没有，没有就删除并报错
   *
   * 为什么报错？因为不应该冗余的，尽可能在服务器"event"的时候就删掉了
   */
  static async cleanIndexes(timeout: number = 50, pageSize: number = 10) {
    // 遍历nodes的索引

    let page = 0;
    let nodes: Array<{ id: string }> = [];

    do {
      console.warn(`Cleaning All Indexes!!!!! page: ${page}`);

      const { rows } = await db.search.advancedSearch('NODE_RESOURCE', {
        query: {
          bool: {
            should: [],
            must: [],
          },
        },
        size: pageSize,
        from: page * pageSize,
      });
      nodes = rows;

      const nodesIds = nodes.map((node) => node.id);
      // 查询数据库中存在的 ID
      const existingRecords = await db.prisma.node.findMany({
        where: {
          id: {
            in: nodesIds,
          },
        },
        select: {
          id: true,
        },
      });

      // 提取存在的 ID
      const existingIds = existingRecords.map((record) => record.id);

      // 找到不存在的 ID
      const nonExistentIds = nodesIds.filter((id) => !existingIds.includes(id));
      if (nonExistentIds && nonExistentIds.length > 0) {
        // 清理他们吧，删删删
        console.warn(
          'Delete and clean node resource search indexs: ',
          nonExistentIds.length,
          nonExistentIds,
        );
        await db.search.bulkDelete(nonExistentIds, 'NODE_RESOURCE', 'exact');
      }

      page++;

      await wait(timeout);
    } while (nodes.length > 0);
  }

  // 这个厉害了，重建所有、所有节点的索引，遍历OLTP的全局，通常用于定时器使用的，每周一次，做检查
  public static async rewriteAllNodeSearchIndex(timeout: number = 50) {
    const pageSize = 10; // 每页的大小
    let page = 0;
    let nodes: NodePO[];

    do {
      console.log(`RewriteAllNodeSearchIndex!!!!! page: ${page}`);

      nodes = await db.prisma.node.findMany({
        skip: page * pageSize,
        take: pageSize,
      });

      await Promise.all(
        nodes.map((node) => {
          console.log('Indexing node:', node.id);
          const nodeSO = NodeSO.initWithModel(node);
          // TODO: check 是否重复
          return AISearchSO.writeNodeSearchIndex(nodeSO);
        }),
      );

      page++;
      await wait(timeout);
    } while (nodes.length > 0);

    console.log(`!!! Done: rewrite all node serch index, page: ${page}`);
  }

  static async writeDatabaseRecords(node: NodeSO, records?: RecordSO[]) {
    if (node.isPrivate) {
      return;
    }
    if (records) {
      // 如果传入了records，直接写入
      await Promise.all(
        records.map(async (recordSO) => {
          await db.search.write({
            id: recordSO.id,
            spaceId: node.spaceId,
            indexData: {
              type: 'RESOURCE_CONTENT',
              contentType: 'DATABASE_RECORD',
              nodeId: node.id,
              recordId: recordSO.id,
              data: JSON.stringify(recordSO.toBO()),
            },
          });
        }),
      );
    } else {
      // 没有传入, 默认写全部记录
      const database = await node.toResourceSO<DatabaseSO>();
      // 一批批的写入
      await database.getRecordsAsStream(
        async (recordSOs) => {
          await Promise.all(
            recordSOs.map(async (recordSO) => {
              await db.search.write({
                id: recordSO.id,
                spaceId: node.spaceId,
                indexData: {
                  type: 'RESOURCE_CONTENT',
                  contentType: 'DATABASE_RECORD',
                  nodeId: node.id,
                  recordId: recordSO.id,
                  data: JSON.stringify(recordSO.toBO()),
                },
              });
            }),
          );
        },
        { pageSize: 100 },
      );
    }
  }

  static async writeDocumentContent(node: NodeSO) {
    try {
      const documentBO = (await node.toBO()) as DocumentBO;
      await db.search.write({
        id: documentBO.id,
        spaceId: node.spaceId,
        indexData: {
          type: 'RESOURCE_CONTENT',
          contentType: 'DOCUMENT_MARKDOWN',
          nodeId: node.id,
          data: documentBO.markdown,
          titleSummary: node.getTitleSummary(),
        },
      });
    } catch (e) {
      console.log('writeDocumentContent error', e);
    }
  }

  static async writeNodeResourceIndex(node: NodeSO) {
    const parents = (await node.getParents()).map((parent) => ({
      id: parent.id,
      name: parent.name,
    }));
    const nodeResourceBO = await node.toBO();

    await db.search.write({
      id: node.id,
      spaceId: node.spaceId,
      // indexSuffixMode: {
      //   custom: node.spaceId, // 后戳
      // },
      indexData: {
        type: 'NODE_RESOURCE',
        id: node.id,
        parents: JSON.stringify(parents),
        nodeResource: JSON.stringify(nodeResourceBO),
        contentSummary: await node.getContentSummary(),
        titleSummary: node.getTitleSummary(),
        resourceType: nodeResourceBO.resourceType,
      },
    });
  }

  // 索引到搜索引擎，挂上space id方便检索，这里异步
  // TODO: 同时发sse，让客户端缓存起来！
  public static async writeNodeSearchIndex(node: NodeSO) {
    if (node.isPrivate) {
      return;
    }
    const promises: Promise<void>[] = [];

    // 写入Resource Content，如果是database record data
    if (node.isDatabase) {
      promises.push(AISearchSO.writeDatabaseRecords(node));
    }

    // 写入document data
    if (node.isDocument) {
      promises.push(AISearchSO.writeDocumentContent(node));
    }

    // 写入整个node node resource
    promises.push(AISearchSO.writeNodeResourceIndex(node));

    await Promise.all(promises);
  }

  public static async rewriteAIChatIndex(chatId: string) {
    const nxKey = `sync:ai:chat:${chatId}`;
    // 防止并发写入
    const result = await db.redis.setNX(nxKey, 1, 300);
    if (!result) {
      return;
    }
    try {
      const chatSO = await AIChatSO.init(chatId);
      if (!chatSO.published) {
        // 判断索引是否存在
        const exists = await db.search.exist(chatId, 'AI_PUBLISHED_CHAT');
        if (exists) {
          // 删除索引数据
          await db.search.delete(chatId, 'AI_PUBLISHED_CHAT', 'exact');
        }
        return;
      }
      const simpleVO = await chatSO.toSimpleVO();
      await db.search.write({
        id: chatId,
        indexData: {
          type: 'AI_PUBLISHED_CHAT',
          id: chatId,
          shareId: chatSO.model.share?.id || '',
          title: simpleVO.title,
          description: simpleVO.description || '',
          createdAt: chatSO.createdAt.getUTCMilliseconds(),
        },
      });
    } catch (e) {
      console.error('syncAIChatIndex error', e);
    } finally {
      await db.redis.del(nxKey, '');
    }
  }

  public static async rewriteAllAIChatsIndex() {
    const nxKey = `sync:ai:chats`;
    // 防止并发写入
    const result = await db.redis.setNX(nxKey, 1, 300);
    if (!result) {
      console.log('rewriteAllAIChatsIndex: already running, skip');
      return;
    }
    try {
      const deleted = await db.search.deleteIndex('AI_PUBLISHED_CHAT');
      if (!deleted) {
        console.error('delete AI_PUBLISHED_CHAT index failed');
      }
      const chatIds = await AIChatSO.getPublishedChatIds();
      await Promise.allSettled(chatIds.map((chatId) => AISearchSO.rewriteAIChatIndex(chatId)));
    } catch (e) {
      console.error('syncAllAIChatsIndex error', e);
    } finally {
      await db.redis.del(nxKey, '');
    }
  }

  public static async aiSearch(
    user: UserSO,
    space: SpaceSO,
    query: string,
    chatHistories: AIMessageVO[],
    scope?: ('NODE_RESOURCES' | 'WEB_PAGE')[],
    advancedModel?: PresetLanguageAIModelDef,
  ): Promise<{
    stream: StreamTextResult<ToolSet, never>;
    searchResult: LauncherCommand[];
  }> {
    const finalScope = scope || ['NODE_RESOURCES', 'WEB_PAGE'];

    let nodeResourcesSearchResult: string | '(NO_RESULT)' = '(NO_RESULT)';
    let webPagesSearchResult: string | '(NO_RESULT)' = '(NO_RESULT)';

    const searchResults: LauncherCommand[] = [];

    if (finalScope.includes('NODE_RESOURCES')) {
      const nodeSearch = await AISearchSO.searchNodesResources(user, space, { query });
      nodeResourcesSearchResult = JSON.stringify(nodeSearch);
      searchResults.push(...nodeSearch);
    }
    if (finalScope.includes('WEB_PAGE')) {
      const pageSearch = await AISearchSO.searchPage(user, space, query);
      webPagesSearchResult = JSON.stringify(pageSearch);
      searchResults.push(...pageSearch);
    }

    const finalPrompt = _.template(PromptAiSearch)({
      language: user.locale,
      chatHistories: JSON.stringify(chatHistories),
      prompt: query,
      nodeResourcesSearchResult,
      webPagesSearchResult,
    });

    // console.log('final prompt', finalPrompt);

    const theModel = advancedModel || getDefaultAIModel();

    const AISO = await import('@bika/domains/ai/server/ai-so').then((mod) => mod.AISO);

    const aistream = AISO.streamText({ user, prompt: finalPrompt }, { model: theModel });
    return {
      stream: await aistream,
      searchResult: searchResults,
    };
  }

  public static async searchContent(user: UserSO, space: SpaceSO, query: string) {
    // : Promise<LauncherCommand[]>
    const limit = 10;
    try {
      const { rows: contents } = await db.search.advancedSearch('RESOURCE_CONTENT', {
        query: {
          bool: {
            should: [
              {
                match: {
                  'indexData.data': query,
                },
              },
            ],
            must: [
              {
                term: {
                  'spaceId.keyword': space.id,
                },
              },
            ],
          },
        },
        // highlight: {
        //   fields: {
        //     'indexData.data': {}, // 指定需要高亮的字段
        //   },
        // },
        size: limit,
      });
      return contents;
    } catch (e) {
      console.error('searchContent error', e);
      return [];
    }
  }

  public static async searchPage(
    user: UserSO,
    space: SpaceSO,
    query: string,
  ): Promise<LauncherCommand[]> {
    const limit = 10;

    const { rows: pages } = await db.search.advancedSearch('WEB_PAGE', {
      query: {
        bool: {
          should: [
            {
              match: {
                'indexData.title': query,
              },
            },
            {
              match: {
                'indexData.content': query,
              },
            },
          ],
          must: [],
        },
      },
      highlight: {
        fields: {
          'indexData.content': {}, // 指定需要高亮的字段
        },
      },
      size: limit,
    });
    const commands: LauncherCommand[] = [];
    for (const page of pages) {
      let highlight = '';
      if (page.highlight && page.highlight['indexData.content']) {
        for (const hl of page.highlight['indexData.content']) {
          highlight += `${hl || ''} `;
        }
      }
      const indexData = WebPageSearchIndexSchema.parse(page.data.indexData);

      commands.push({
        type: 'URL',
        text: indexData.title || 'Unknown Title',
        highlight,
        url: process.env.APP_HOSTNAME + indexData.url, // 组合成完整路径
      });
    }
    return commands;
  }

  public static async queryNodes(
    spaceId: string,
    param?: {
      query?: string;
      resourceType?: NodeResourceType;
      limit?: number;
      highlight?: estypes.SearchHighlight;
    },
  ) {
    const { query, resourceType, limit = 10, highlight } = param || {};
    // 检查索引
    const must: QueryDslQueryContainer[] = [
      {
        term: {
          'spaceId.keyword': spaceId,
        },
      },
    ];
    const should: QueryDslQueryContainer[] = [];
    if (query) {
      should.push({
        match_phrase_prefix: {
          'indexData.titleSummary': query,
        },
      });
      should.push({
        match: {
          'indexData.contentSummary': query,
        },
      });
      should.push({
        match_phrase_prefix: {
          'indexData.contentSummary': query,
        },
      });
      must.push({
        bool: {
          should,
        },
      });
    }

    if (resourceType) {
      must.push({
        term: {
          'indexData.resourceType.keyword': resourceType,
        },
      });
    }
    const { rows } = await db.search.advancedSearch('NODE_RESOURCE', {
      query: {
        bool: {
          must,
        },
      },
      highlight,
      size: limit,
    });
    return rows;
  }

  /**
   * 去ES搜索节点资源
   *
   * @returns
   */
  public static async searchNodesResources(
    user: UserSO,
    space: ISpaceSO,
    param?: { query?: string; resourceType?: NodeResourceType },
  ): Promise<LauncherCommand[]> {
    // const db.search.search('NODE_RESOURCE', {
    //   spaceId: space.model.spaceId,
    // });
    const locale = user.locale;

    const searchNodes = await AISearchSO.queryNodes(space.id, {
      ...param,
      highlight: {
        fields: {
          'indexData.contentSummary': {}, // 指定需要高亮的字段
          'indexData.titleSummary': {
            number_of_fragments: 0,
          },
        },
      },
    });

    const nodeParentMap: { [key: string]: { id: string; name: iString }[] } = {};

    const nodeHighlightMap: { [key: string]: string | undefined } = {};

    const nodeTitleHighlightMap: { [key: string]: string | undefined } = {};

    const nodeIds = searchNodes.map((node) => {
      let highlight = '';
      if (node.highlight) {
        if (node.highlight['indexData.contentSummary']) {
          highlight = node.highlight['indexData.contentSummary'].join(' ');
        }
        if (node.highlight['indexData.titleSummary']) {
          try {
            const tileSummary = JSON.parse(node.highlight['indexData.titleSummary'][0]) as iString;
            nodeTitleHighlightMap[node.id] = iStringParse(tileSummary, locale);
          } catch (_e) {
            nodeTitleHighlightMap[node.id] = node.highlight['indexData.titleSummary'][0];
          }
        }
      }
      if (node.data.indexData.type === 'NODE_RESOURCE') {
        nodeParentMap[node.id] = JSON.parse(node.data.indexData.parents || '[]');
        nodeHighlightMap[node.id] = highlight;
      }
      return node.id;
    });

    const nodePOs = await db.prisma.node.findMany({
      where: {
        id: {
          in: nodeIds,
        },
      },
    });

    const nodePOMap = _.keyBy(nodePOs, 'id');

    const commands: LauncherCommand[] = [];
    // 这里用index 查出来的顺序，因为是按照score排序的
    for (const nodeId of nodeIds) {
      const nodePO = nodePOMap[nodeId];
      if (!nodePO) {
        continue;
      }
      const nodeSO = await NodeSO.initWithModel(nodePO);
      const nodeTreeVO = await nodeSO.toVO({ locale, userId: user.id });
      if (canReadNode(nodeTreeVO)) {
        const parentNames = nodeParentMap[nodePO.id]
          ?.reverse()
          .map((node) => iStringParse(node.name, locale));
        commands.push({
          type: 'NODE_MENU',
          text: nodeTitleHighlightMap[nodeTreeVO.id] || nodeTreeVO.name,
          node: {
            ...nodeTreeVO,
            path: parentNames?.join('/'),
            name: nodeTitleHighlightMap[nodeTreeVO.id] || nodeTreeVO.name,
            // 确保 type 字段符合 NodeResourceTypeSchema 的要求
            type: nodeTreeVO.type,
          },
          url: `${process.env.APP_HOSTNAME}/spaces/${space.id}/node/${nodePO.id}`,
          highlight: nodeHighlightMap[nodePO.id],
        });
      }
    }

    return commands;
  }

  public static async searchDatabaseRecords(
    user: UserSO,
    space: ISpaceSO,
    query?: string,
  ): Promise<LauncherCommand[]> {
    const locale = user.locale;
    // 检查索引
    const must: QueryDslQueryContainer[] = [
      {
        term: {
          'spaceId.keyword': space.id,
        },
      },
      {
        term: {
          'indexData.contentType.keyword': 'DATABASE_RECORD',
        },
      },
    ];
    const should: QueryDslQueryContainer[] = [];
    if (query) {
      should.push({
        match_phrase_prefix: {
          'indexData.data': query,
        },
      });
      must.push({
        bool: {
          should,
        },
      });
    }
    const { rows: searchContents } = await db.search.advancedSearch('RESOURCE_CONTENT', {
      query: {
        bool: {
          must,
        },
      },
      highlight: {
        fields: {
          'indexData.data': {
            number_of_fragments: 0,
          },
        },
      },
      size: 10,
    });
    const databaseIds = searchContents
      .map((item) => {
        if (
          item.data.indexData.type === 'RESOURCE_CONTENT' &&
          item.data.indexData.contentType === 'DATABASE_RECORD'
        ) {
          return item.data.indexData.nodeId;
        }
        return undefined;
      })
      .filter((item) => item !== undefined) as string[];

    const databases = await DatabaseSO.getDatabases(space.id, databaseIds);
    const databaseMap = _.keyBy(databases, 'id');
    // return commands
    const commands: LauncherCommand[] = [];
    for (const content of searchContents) {
      if (
        content.data.indexData.type === 'RESOURCE_CONTENT' &&
        content.data.indexData.contentType === 'DATABASE_RECORD'
      ) {
        const databaseId = content.data.indexData.nodeId;
        let highlight = '';
        if (content.highlight && content.highlight['indexData.data']) {
          const data = JSON.parse(content.highlight['indexData.data'][0]);
          highlight = AISearchSO.flatObj(data.values || data.data, '<em>');
        }
        commands.push({
          type: 'DATABASE_RECORD',
          record: {
            id: content.id,
            databaseId,
          },
          highlight,
          text: iStringParse(databaseMap[databaseId].name, locale),
          url: `${process.env.APP_HOSTNAME}/spaces/${space.id}/node/${databaseId}/record/${content.id}`,
        });
      }
    }
    return commands;
  }

  public static async searchDocuments(
    user: UserSO,
    space: ISpaceSO,
    query?: string,
  ): Promise<LauncherCommand[]> {
    const locale = user.locale;
    // 检查索引
    const must: QueryDslQueryContainer[] = [
      {
        term: {
          'spaceId.keyword': space.id,
        },
      },
      {
        term: {
          'indexData.contentType.keyword': 'DOCUMENT_MARKDOWN',
        },
      },
    ];
    const should: QueryDslQueryContainer[] = [];
    if (query) {
      should.push({
        match: {
          'indexData.data': query,
        },
      });
      should.push({
        match_phrase_prefix: {
          'indexData.titleSummary': query,
        },
      });
      must.push({
        bool: {
          should,
        },
      });
    }
    const { rows: searchContents } = await db.search.advancedSearch('RESOURCE_CONTENT', {
      query: {
        bool: {
          must,
        },
      },
      highlight: {
        fields: {
          'indexData.data': {},
          'indexData.titleSummary': {
            number_of_fragments: 0,
          },
        },
      },
      size: 10,
    });
    const commands: LauncherCommand[] = [];
    for (const doc of searchContents) {
      let title = 'Document';
      let highlight = '';
      if (doc.highlight && doc.highlight['indexData.data']) {
        highlight = doc.highlight['indexData.data'].join(' ');
      }
      if (doc.highlight && doc.highlight['indexData.titleSummary']) {
        try {
          const tileSummary = JSON.parse(doc.highlight['indexData.titleSummary'][0]) as iString;
          title = iStringParse(tileSummary, locale);
        } catch (_e) {
          title = doc.highlight['indexData.titleSummary'][0];
        }
      }
      commands.push({
        type: 'DOCUMENT',
        text: title,
        highlight,
        id: doc.id,
      });
    }
    return commands;
  }

  public static async searchPublishedAIChats(
    param: PublishedChatPaginationDTO,
  ): Promise<AIChatPaginationVO> {
    const { pageNo, pageSize, keyword } = param;
    // es page query, keyword is title or description
    const { rows, total } = await db.search.advancedSearch('AI_PUBLISHED_CHAT', {
      query: {
        ...(keyword
          ? {
              multi_match: {
                fields: ['indexData.title', 'indexData.description'],
                query: keyword,
                type: 'phrase_prefix',
              },
            }
          : { match_all: {} }),
      },
      // highlight: {
      //   fields: {
      //     'indexData.description': {},
      //     'indexData.title': {
      //       number_of_fragments: 0,
      //     },
      //   },
      // },
      size: pageSize,
      from: (pageNo - 1) * pageSize,
    });

    const pagination = { pageNo, pageSize, total };
    const data = rows
      .map((r) => {
        const { data, success } = AIPublishedChatSearchIndexSchema.safeParse(r.data.indexData);
        if (success) {
          return {
            id: r.id,
            title: data.title,
            description: data.description,
            createdAt: new Date(data.createdAt).toISOString(),
            share: {
              id: data.shareId,
              scope: 'PUBLISH',
            },
          };
        }
        return undefined;
      })
      .filter((item) => item !== undefined) as AIChatSimpleVO[];
    return { pagination, data };
  }

  // biome-ignore lint/suspicious/noExplicitAny: <no need to be explicit>
  private static flatObj(data: any, literature?: string): string {
    const value = Object.values(data).map((v) => {
      if (typeof v === 'string') {
        if (literature && !v.includes(literature)) {
          return undefined;
        }
        return v;
      }
      return AISearchSO.flatObj(v, literature);
    });
    return value.filter((i) => i).join(' ');
  }
}
