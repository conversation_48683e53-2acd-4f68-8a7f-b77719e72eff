import { useTR<PERSON>Query } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import type { AIIntentParams, AIMessageBO } from '@bika/types/ai/bo';
import type { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { useSpaceContextForce } from '@bika/types/space/context';
import { IconButton } from '@bika/ui/button';
import { Link } from '@bika/ui/form-components';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import ChevronUpOutlined from '@bika/ui/icons/components/chevron_up_outlined';
import CopyOutlined from '@bika/ui/icons/components/copy_outlined';
import { Stack } from '@bika/ui/layouts';
import { snackbarShow } from '@bika/ui/snackbar';
import { styled } from '@bika/ui/styled';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';
import Box from '@mui/joy/Box';
import { type DynamicToolUIPart, getToolName, isToolUIPart, type ToolUIPart } from 'ai';
import React, { memo } from 'react';
import { Message } from '../wizard/message';
import { MessageIcon } from './ai-message-icon';
import { AIMessageReasoning } from './ai-message-reasoning';
import { MessageAttachmentRenderer as AttachmentRenderer } from './attachment/message-attachment-renderer';
import { AIChatContextNode } from './chat-context/ai-chat-context-node';
import { ToolUI } from './tools/ai-tool-ui';
import type { ToolResultVO } from './tools/type';
import { getAttachmentDisplayPath } from './utils/utils';

export { getAttachmentDisplayPath };
// Helper function to get attachment display path for experimental_attachments

type ToolCallId = string;
interface Props {
  chatId: string;
  message: AIMessageBO;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  disabled: boolean;
  // TODO :  这里定义含糊了， 需要讨论下
  toolExecuteErrors?: Record<ToolCallId, string>;
  sendUI: (uiResolve: AIIntentUIResolveDTO) => Promise<void>;
  sendMessage: (message: string) => Promise<void>;
  onSelectTool?: (uiPart: ToolUIPart | DynamicToolUIPart, uiMessage: AIMessageBO) => void;
  onCloseTool?: () => void;
  addToolResult: (args: { tool: string; toolCallId: string; output: ToolResultVO }) => void;
  skillsets: SkillsetSelectDTO[];

  /**
   * Selecting / Selected Tool ID
   */
  artifactToolId?: string;
  initAIIntent?: AIIntentParams;
}

const StyledAttachmentRenderer = styled(AttachmentRenderer)({
  marginBottom: '8px',
});

export const AIChatMessage = memo(function AIChatMessage(props: Props) {
  const m = props.message;
  // const msgAnnotations = React.useMemo(() => new AIMessageAnnotations(m.annotations), [m.annotations]);

  const sourceParts = m.parts.filter((p) => p.type === 'source-url');

  const [showSource, setShowSource] = React.useState(false);
  const locale = useLocale();
  const { i: iStr, t } = locale;
  const spaceContext = useSpaceContextForce();
  const { myInfo } = spaceContext || {};
  const trpcQuery = useTRPCQuery();
  const remoteExecuteTool = trpcQuery.ai.executeTool.useMutation();

  const renderUserMessage = () => {
    // if (m.content) {
    //   if (m.content.startsWith('/resolve:')) {
    //     // 取后面的JSON字符串
    //     const jsonStr = m.content.substring('/resolve:'.length);
    //     try {
    //       const json = JSON.parse(jsonStr);
    //       if (json.type === 'UI') {
    //         const uiResolve = json.uiResolve as AIIntentUIResolveDTO;
    //         switch (uiResolve.type) {
    //           case 'CHOICES': {
    //             return uiResolve.choiceOption?.render || m.content;
    //           }
    //           case 'FLOW': {
    //             return uiResolve.response;
    //           }
    //           default: {
    //             return json.uiResolve.text;
    //           }
    //         }
    //       }
    //     } catch (_e) {
    //       return m.content;
    //     }
    //   }
    //   return m.content;
    // }
    // 有可能 m.content 为空，但是 m.parts 有值
    if (m.parts.length > 0) {
      return m.parts
        .map((part) => {
          if (part.type === 'text') {
            return part.text;
          }
          return '';
        })
        .join('');
    }

    // if (m.annotations && m.annotations?.length > 0) {
    //   return '';
    // }

    return 'Error';
  };

  const firstAiConsultingIndex = m.parts.findIndex(
    (part) => isToolUIPart(part) && getToolName(part).startsWith('ai-consulting'),
  );

  const renderMessage = () => {
    const messageChatContexts = m.metadata?.contexts || [];
    if (m.role === 'user') {
      const userMessage = renderUserMessage();
      return (
        <Stack key={m.id} display="flex" flexDirection="column" justifyContent="flex-end">
          <Box
            sx={{
              whiteSpace: 'pre-wrap',
              display: 'flex',
              alignSelf: 'end',
              flexDirection: 'row',
              alignItems: 'center',
              mb: 1,
            }}
          >
            <Typography level="b3" textColor="var(--text-secondary)">
              {m.metadata?.creator?.name || myInfo?.name}
            </Typography>
            <MessageIcon type="user" user={m.metadata?.creator || myInfo} />
          </Box>
          <Box
            sx={{
              whiteSpace: 'pre-wrap',
              display: 'flex',
              alignItems: 'flex-end',
              flexDirection: 'column',
            }}
          >
            {messageChatContexts &&
              messageChatContexts.length > 0 &&
              messageChatContexts.map((chatContext, idx) => {
                if (chatContext.type === 'attachment') {
                  const attach = chatContext.attachment;
                  return (
                    <StyledAttachmentRenderer key={`${attach.name}-${idx}`} attachment={attach} />
                  );
                }
                if (chatContext.type === 'node') {
                  return (
                    <Box key={`${chatContext.node.id}-${idx}`} mb={1}>
                      <AIChatContextNode value={chatContext} />
                    </Box>
                  );
                }
                return null;
              })}

            {!!userMessage && (
              <Stack
                sx={{
                  fontSize: '16px',
                  lineHeight: '22px',
                  borderRadius: '8px',
                  backgroundColor: 'var(--bg-popup)',
                  color: 'var(--text-primary)',
                  py: 2,
                  px: 1,
                  padding: '12px 16px',
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word',
                }}
              >
                {userMessage}
              </Stack>
            )}
          </Box>
        </Stack>
      );
    }
    if (m.role === 'assistant' || m.role === 'system') {
      return (
        <Stack key={m.id} display="flex" flexDirection="column">
          <Box
            sx={{
              mb: 1,
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <MessageIcon type="assistant" initAIIntent={props.initAIIntent} />
            <Typography level="b3" textColor="var(--text-secondary)" sx={{ ml: 1 }}>
              {props.initAIIntent?.type === 'AI_NODE' ? props.initAIIntent.nodeName : ''}
              {props.initAIIntent?.type === 'BUILDER' || props.initAIIntent?.type === 'SUPERVISOR'
                ? iStr(props.initAIIntent.name)
                : ''}
              {props.initAIIntent?.type === 'COPILOT' ? t.global.copilot.title : ''}
            </Typography>
          </Box>

          <Stack
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              overflow: 'hidden',
            }}
          >
            {/* Sources */}
            {sourceParts.length > 0 && (
              <Stack mb={1}>
                <Stack
                  sx={{
                    display: 'flex',
                    cursor: 'pointer',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    width: 'max-content',
                    borderRadius: '8px',
                    p: 1,
                    color: 'var(--text-primay)',
                    backgroundColor: 'var(--bg-controls)',
                    maxWidth: '80%',
                    '&:hover': {
                      backgroundColor: 'var(--bg-controls-hover)',
                    },
                  }}
                  onClick={() => {
                    setShowSource(!showSource);
                  }}
                >
                  <Typography mr={1} textColor="var(--text-secondary)" level="b4">
                    {t('ai.reference', { count: sourceParts.length })}
                  </Typography>
                  {showSource ? (
                    <ChevronUpOutlined color={'var(--text-secondary)'} />
                  ) : (
                    <ChevronDownOutlined color={'var(--text-secondary)'} />
                  )}
                </Stack>
                {/* showSource */}

                {showSource &&
                  sourceParts.map((part) => (
                    <Link my={0.5} key={part.sourceId} target="_blank" href={part.url}>
                      {part.title || part.url}
                    </Link>
                  ))}
              </Stack>
            )}

            {/* Reasoning & Messages & Tools */}
            <Stack gap={2}>
              {m.parts
                .filter((part) => {
                  if (part.type === 'text' && part.text.replace(/\n/g, '') === '') {
                    return false;
                  }
                  if (part.type === 'step-start') {
                    // 过滤掉 step-start
                    return false;
                  }
                  return true;
                })
                .map((part, idx2) => (
                  <Box key={`part-${idx2}-${part.type}`}>
                    {/* Reasoning */}
                    {part.type === 'reasoning' && <AIMessageReasoning part={part} />}

                    {/* Text */}
                    {part.type === 'text' && (
                      <Stack
                        sx={{
                          alignSelf: 'flex-start',
                          fontSize: '16px',
                          borderRadius: '8px',
                          py: 1,
                        }}
                      >
                        <Message text={iStr(part.text)} />
                        {props.status === 'ready' &&
                          !!iStr(part.text) &&
                          iStr(part.text).length > 100 && (
                            <Tooltip title={t.copy.copy}>
                              <IconButton
                                variant="outlined"
                                color="neutral"
                                size="sm"
                                sx={{
                                  mt: 1,
                                  width: 'fit-content',
                                }}
                                onClick={() => {
                                  navigator.clipboard
                                    .writeText(iStr(part.text))
                                    .then(() => {
                                      snackbarShow({
                                        content: t.copy.copy_success,
                                        color: 'success',
                                      });
                                    })
                                    .catch((err) => {
                                      console.error('Failed to copy text: ', err);
                                    });
                                }}
                              >
                                <CopyOutlined color="var(--text-secondary)" />
                              </IconButton>
                            </Tooltip>
                          )}
                      </Stack>
                    )}

                    {/* File */}
                    {part.type === 'file' && <>{JSON.stringify(part, null, 2)}</>}

                    {/* Tools */}
                    {(isToolUIPart(part) || part.type === 'dynamic-tool') && (
                      <Stack>
                        {/* {toolParts.map((part, idx2) => ( */}
                        <ToolUI
                          executeToolResult={async (toolCallId: string) => {
                            const remoteToolResult = await remoteExecuteTool.mutateAsync({
                              toolCallId,
                              chatId: props.chatId, // 传入 messageId，避免 toolCallId 重复
                            });

                            console.log('executeToolResult', toolCallId, remoteToolResult);
                            return remoteToolResult;
                          }}
                          addToolResult={props.addToolResult}
                          skillsets={props.skillsets}
                          part={part}
                          key={idx2}
                          error={props.toolExecuteErrors?.[part.toolCallId]}
                          hideFlow={firstAiConsultingIndex === idx2}
                          isHighlight={props.artifactToolId === part.toolCallId}
                          disabled={props.disabled} // 除了最后 1 个UI，其它全部disabled，不能交互
                          sendUI={props.sendUI}
                          sendMessage={props.sendMessage}
                          onCloseTool={props.onCloseTool}
                          onClickTool={(toolUI) => {
                            if (props.onSelectTool) {
                              props.onSelectTool(toolUI, m);
                            }
                          }}
                        />
                      </Stack>
                    )}
                  </Box>
                ))}
            </Stack>
          </Stack>
        </Stack>
      );
    }

    return null;
  };

  return (
    <Stack mb={'24px'} sx={{ width: '100%', maxWidth: '768px' }} key={m.id}>
      {renderMessage()}
    </Stack>
  );
});
