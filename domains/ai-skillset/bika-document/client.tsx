import type { NodeCreateDTO } from '@bika/types/node/dto';
import assert from 'assert';
import { get } from 'lodash';
import { DefaultArtifact } from '../../ai/client/chat/artifacts/default-artifact';
import { MarkdownArtifact } from '../../ai-artifacts/text-server-artifact/markdown-artifact';
import type { SkillsetUIMap } from '../types';
import { BikaDocumentSkillsetNameEnum } from './type';

const skillsetName = BikaDocumentSkillsetNameEnum.Enum;
const DocumentToolsetUI: SkillsetUIMap = {
  [skillsetName.create_document]: {
    artifact: ({ toolInvocation, skillsets }) => {
      const content = get((toolInvocation as any).args, 'doc', {});
      const isMarkdown = content?.sourceType === 'markdown';
      const title = get(toolInvocation, 'output.name', '');

      if (isMarkdown) {
        // Get the markdown content from the document
        const markdownContent = content?.markdown || '';
        return (
          <MarkdownArtifact
            value={{
              content: markdownContent,
              title,
            }}
            skillsets={skillsets}
            tool={toolInvocation}
          />
        );
      }
      return (
        <DefaultArtifact resources={toolInvocation} tool={toolInvocation} skillsets={skillsets} />
      );
    },
    clientExecute: async (toolInvocation, context) => {
      if (toolInvocation.state !== 'input-available') {
        throw new Error('Tool invocation state is not call');
      }
      if (!context || !context.apiCaller) {
        throw new Error('Context is required');
      }
      const { spaceId, doc, parentId } = toolInvocation.input as {
        spaceId: string;
        doc: any;
        parentId: string | undefined;
      };
      const { trpc } = context.apiCaller;

      let newParentId = parentId;
      if (!parentId) {
        const rootNode = await trpc.space.getRootNode.query({
          spaceId,
        });
        newParentId = rootNode.id;
      }
      assert(newParentId, 'New parent ID is required');

      const docCreateDTO = async (): Promise<NodeCreateDTO | undefined> => {
        let markdown: string | undefined;
        if (doc.sourceType === 'artifact') {
          const artifact = await trpc.ai.fetchArtifact.query({
            artifactId: doc.artifactId,
          });
          if (artifact?.type === 'text' && typeof artifact.data === 'string') {
            markdown = artifact.data;
          }
        } else {
          markdown = doc.markdown;
        }
        if (!markdown) {
          return undefined;
        }
        return {
          spaceId,
          parentId: newParentId,
          data: {
            resourceType: 'DOCUMENT',
            name: doc.name,
            description: doc.description,
            markdown,
          },
        };
      };
      const createDTO = await docCreateDTO();
      if (!createDTO) {
        return;
      }
      context.setData({
        toolInvocation,
        dto: [createDTO],
        process: 0,
      });

      const newNode = await trpc.node.create.mutate(createDTO);
      context.setData({
        toolInvocation,
        dto: [createDTO],
        process: 1,
        redirectNodeId: newNode.id,
      });
    },
  },
};

export default DocumentToolsetUI;
