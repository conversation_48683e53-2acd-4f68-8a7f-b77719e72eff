import type { AIMessagePartText } from '@bika/types/ai/bo';
import { type TavilyClient, tavily } from '@tavily/core';
import { inputFieldsToZodSchema } from '@toolsdk.ai/sdk-ts/utils';
import type { Tool, ToolSet } from 'ai';
import { isInCI } from 'sharelib/app-env';
import { z } from 'zod';
import { ToolSDKAISO } from '../../automation-nodes/toolsdk-action/toolsdk-ai-so';
import { SpaceAttachmentSO } from '../../space/server/space-attachment-so';
import type { SpaceSO } from '../../space/server/space-so';
import type { SkillsetHandlerContext, ToolSetHandler } from '../types';

type BraveSearchResult = {
  content: (
    | {
        type: 'image';
        data: string;
        mimeType: string;
      }
    | AIMessagePartText
  )[];
};
const fetchBraveSearchMCPToolSet = async (ctx?: SkillsetHandlerContext): Promise<ToolSet> => {
  const packageKey = 'brave-search-mcp';
  const packageVersion = undefined;
  const brave = await ToolSDKAISO.getPackage(packageKey, packageVersion);
  if (!brave) {
    return {};
  }

  const imageSearchToolKey = 'brave_image_search';
  const braveImageSearch = brave.tools.find((tool) => tool.key === imageSearchToolKey);
  if (!braveImageSearch) {
    return {};
  }
  const tool: Tool = {
    description: braveImageSearch.description,
    inputSchema: inputFieldsToZodSchema(braveImageSearch.inputFields),
    execute: async (params) => {
      const result = await ToolSDKAISO.runPackageTool<BraveSearchResult>({
        packageKey,
        packageVersion,
        envs: {
          BRAVE_API_KEY: process.env.BRAVE_API_KEY || '',
        },
        body: {
          toolKey: imageSearchToolKey,
          inputData: params,
        },
      });
      const { space, user } = ctx || {};
      if (space && user) {
        // 把图片的base64编码的图片上传到s3, 解决token超限问题
        const { content } = result;
        result.content = await Promise.all(
          content.map(async (item) => {
            if (item.type !== 'image') {
              return item;
            }
            const image = Buffer.from(item.data, 'base64');
            const attachment = await SpaceAttachmentSO.uploadAttachmentsByBufferObject(
              user.id,
              space as SpaceSO,
              [{ buffer: image, contentType: item.mimeType, size: image.length }],
              'ai-images/',
            );
            return {
              type: 'image',
              data: attachment[0].fullUrl,
              mimeType: item.mimeType,
            };
          }),
        );
      }

      // 返回新的result
      return result;
    },
  };
  return { bika_search_images: tool };
};

const createBikaSearchPagesTool = (tvly: TavilyClient): Tool => ({
  description: 'Search for web pages based on a query.',
  inputSchema: z.object({
    query: z.string().describe('The search query to search pages'),
    auto_parameters: z
      .boolean()
      .optional()
      .describe(
        'When auto_parameters is enabled, Tavily automatically configures search parameters based on your query’s content and intent.',
      ),
    searchDepth: z
      .enum(['basic', 'advanced'])
      .default('basic')
      .optional()
      .describe('The depth of the search. It can be "basic" or "advanced".'),
    topic: z
      .enum(['general', 'news', 'finance'])
      .default('general')
      .optional()
      .describe('The category of the search. Determines which agent will be used.'),
    days: z
      .number()
      .default(7)
      .optional()
      .describe(
        'The number of days back from the current date to include in the results. Available only when using the "news" topic.',
      ),
    timeRange: z
      .enum(['day', 'week', 'month', 'year'])
      .default('day')
      .optional()
      .describe(
        'The time range back from the current date. Allowed values: "day", "week", "month", "year".',
      ),
    maxResults: z
      .number()
      .int()
      .min(0)
      .max(20)
      .default(10)
      .optional()
      .describe('The maximum number of search results to return.'),
    includeDomains: z
      .array(z.string())
      .default([])
      .optional()
      .describe('A list of domains to specifically include in the search results.'),
    excludeDomains: z
      .array(z.string())
      .default([])
      .optional()
      .describe('A list of domains to specifically exclude from the search results.'),
    timeout: z
      .number()
      .default(60)
      .optional()
      .describe('A timeout (seconds) to be used in requests to the Tavily API.'),
  }),
  execute: async (params) => {
    const { query, ...restParams } = params;
    const response = await tvly.search(query, {
      includeFavicon: true,
      ...restParams,
    });
    return response;
  },
});
const tools: ToolSetHandler = async (ctx?: SkillsetHandlerContext) => {
  if (isInCI()) {
    return Promise.resolve({});
  }

  const [braveToolSet] = await Promise.all([fetchBraveSearchMCPToolSet(ctx)]);

  const TAVILY_API_KEY = process.env.TAVILY_API_KEY || '';
  const tvly = tavily({ apiKey: TAVILY_API_KEY });

  return {
    bika_search_pages: createBikaSearchPagesTool(tvly),
    ...braveToolSet,
  };
};

export default tools;
