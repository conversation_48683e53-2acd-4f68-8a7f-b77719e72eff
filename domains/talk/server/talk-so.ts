import { AIChatSO } from '@bika/domains/ai/server/ai-chat/ai-chat-so';
import { AISkillsetServerRegistry } from '@bika/domains/ai-skillset/server-registry';
import { SseSO } from '@bika/domains/event/server/sse/sse-so';
import { NodeController } from '@bika/domains/node/apis';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db } from '@bika/server-orm';
import type { TalkModel } from '@bika/server-orm/mongo/talk-model';
import type { NodeRenderOpts } from '@bika/types/node/vo';
import type { SkillsetVO } from '@bika/types/skill/vo';
import type { TalkBO, TalkExpertKey, TalkType } from '@bika/types/space/bo';
import type { TalkMutateDTO } from '@bika/types/space/dto';
import { type SpaceMyRedDotsVO, type TalkDetailVO, TalkDetailVOSchema } from '@bika/types/space/vo';
import type { RecipientProps } from '@bika/types/system';
import assert from 'assert';
import type { LocaleType } from 'basenext/i18n';
import _ from 'lodash';
import { AINodeSO } from '../../node-resources/ai-agent/ai-node-so';

type TalkPO = Pick<TalkModel, 'id' | 'data' | 'createdAt' | 'updatedAt' | 'isPinned'>;

/**
 * Chat Feeds 聊天流
 */
export class TalkSO {
  private _model: TalkPO;

  private constructor(private model: TalkPO) {
    this._model = model;
  }

  public get id() {
    return this._model.id;
  }

  public get talkType(): TalkType {
    return this._model.data.type;
  }

  public get nodeId(): string | undefined {
    if (this.talkType === 'node') {
      return this._model.data.nodeId;
    }
    return undefined;
  }

  static async upsertByExpertKey(expertKey: TalkExpertKey, member: MemberSO) {
    const userId = member.userId;
    const feedSO = await TalkSO.upsert(
      {
        type: 'expert',
        expertKey,
      },
      member.id,
    );

    const feedVO = await feedSO.toVO();

    SseSO.emit(userId, { name: 'talk', talk: feedVO!, memberId: member.id });

    return feedSO;
  }

  static async upsertByNode(node: NodeSO, user: UserSO) {
    const member = await user.getMember(node.spaceId);
    const feedSO = await TalkSO.upsert(
      {
        type: 'node',
        nodeId: node.id,
        nodeType: node.type,
      },
      member.id,
    );

    const feedVO = await feedSO.toNodeTalkVO(node, user.locale);

    SseSO.emit(user.id, { name: 'talk', talk: feedVO, memberId: member.id });

    return feedSO;
  }

  static generateId(feedDTO: TalkMutateDTO) {
    if (feedDTO.type === 'node') {
      return `${feedDTO.type}_${feedDTO.nodeId}`;
    }
    if (feedDTO.type === 'expert') {
      return `${feedDTO.type}_${feedDTO.expertKey}`;
    }
    console.error('Error on ', feedDTO);
    throw new Error('not implemented');
  }

  /**
   *  某个数据被接触后， 包括增删改查，就 马上接着一条 feed
   *
   * 有三种逻辑：
   * 1.  仅仅按了一下，count不变，聊天流置顶
   * 2. 业务逻辑增加， 比如发送信息的 mission，count ++ (客户端发起点击，可清调这个 count)
   */
  static async upsert(feedDTO: TalkMutateDTO, memberId: string, count?: number) {
    const recipient: RecipientProps = {
      recipientType: 'MEMBER',
      recipientId: memberId,
    };

    // id 是组合的
    const id = TalkSO.generateId(feedDTO);
    let feedPO = await db.mongo.talk(recipient.recipientId).findOneAndUpdate(
      {
        id,
        'recipient.recipientType': recipient.recipientType,
        'recipient.recipientId': recipient.recipientId,
      },
      {
        $set: {
          id,
          recipient,
          data: feedDTO,
          // createdAt: new Date(),
          updatedAt: new Date(),
        },
        $inc: count ? { count } : { count: 0 },
      },
    );

    if (!feedPO) {
      feedPO = await db.mongo.talk(recipient.recipientId).create({
        id,
        recipient,
        data: feedDTO,
        // createdAt: new Date(),
        updatedAt: new Date(),
        count: 1,
      });
      assert(feedPO, `feedPO create failed : ${id}`);
    }

    assert(feedPO, `feedPO should not be null: ${id}`);
    return new TalkSO(feedPO);
  }

  //
  static async list(recipient: RecipientProps): Promise<TalkSO[]> {
    const feedsPOs = await db.mongo
      .talk(recipient.recipientId)
      .find({
        'recipient.recipientType': recipient.recipientType,
        'recipient.recipientId': recipient.recipientId,
      })
      .sort({
        isPinned: -1, // 置顶的在前面
        updatedAt: -1, // 按更新时间倒序
      });

    if (feedsPOs) {
      return feedsPOs.map((feed) => new TalkSO(feed));
    }
    return [];
  }

  public async toNodeTalkVO(
    node: NodeSO,
    locale?: NodeRenderOpts['locale'],
  ): Promise<TalkDetailVO> {
    const nodeVO = await node.toVO({ locale });
    let badge: string | undefined;
    const skillsets: SkillsetVO[] = [];
    if (node.type === 'AI') {
      const aiNode = await node.toResourceSO<AINodeSO>();
      const aiNodeSkillsets = await aiNode.getSkillsets(locale);
      skillsets.push(...aiNodeSkillsets);
      // query unread chat count
      const unreadCount = await AIChatSO.getUnreadChatCount(aiNode.id);
      badge = unreadCount > 0 ? unreadCount.toString() : undefined;
    }

    return {
      id: this.id,
      type: 'node',
      nodeId: nodeVO.id,
      nodeType: nodeVO.type,
      node: nodeVO,
      createdAt: this._model.createdAt.toISOString(),
      updatedAt: this._model.updatedAt.toISOString(),
      isPinned: this._model.isPinned,
      skillsets,
      badge,
    };
  }

  private async toUnitTalkVO(aiNode: AINodeSO | null, locale?: LocaleType): Promise<TalkDetailVO> {
    assert(this.bo.type === 'unit', 'bo.type should be unit');
    const skillsets: SkillsetVO[] = [];
    if (aiNode) {
      const aiNodeSkillests = await aiNode.getSkillsets(locale);
      skillsets.push(...aiNodeSkillests);
    }
    return {
      id: this.id,
      type: 'unit',
      unitId: this.bo.unitId,
      createdAt: this._model.createdAt.toISOString(),
      updatedAt: this._model.updatedAt.toISOString(),
      isPinned: this._model.isPinned,
      skillsets,
    };
  }

  private async toExpertVOWithReddot(reddot?: SpaceMyRedDotsVO): Promise<TalkDetailVO> {
    const anyUser = await UserSO.admin();
    const bo = this._model.data as TalkBO;
    assert(bo.type === 'expert', 'bo.type should be expert');

    let badge: string | undefined;
    const skillsets: SkillsetVO[] = [];

    // const badge = () => {
    switch (bo.expertKey) {
      case 'supervisor':
        {
          badge = reddot?.home?.toString();
          const superAgentSkillsetVO = await AISkillsetServerRegistry.getSkillsetVO(anyUser, {
            kind: 'preset',
            key: 'bika-super-agent',
          });
          skillsets.push(superAgentSkillsetVO!);
        }
        break;
      case 'builder': {
        {
          const appBuilderSkillsetVO = await AISkillsetServerRegistry.getSkillsetVO(anyUser, {
            kind: 'preset',
            key: 'bika-app-builder',
          });
          skillsets.push(appBuilderSkillsetVO!);
        }
        break;
      }
      case 'mission':
        badge = reddot?.todos?.toString();
        break;
      case 'report':
        badge = reddot?.reports?.toString();
        break;
      default:
        badge = '0';
        break;
    }
    // };

    return {
      id: this.id,
      type: 'expert',
      expertKey: bo.expertKey,
      createdAt: this._model.createdAt.toISOString(),
      updatedAt: this._model.updatedAt.toISOString(),
      badge,
      isPinned: this._model.isPinned,
      skillsets,
    };
  }

  public get bo(): TalkBO {
    const bo = this._model.data as TalkBO;
    return bo;
  }

  public async toVO(opts?: NodeRenderOpts): Promise<TalkDetailVO | null> {
    const { locale } = opts || { locale: 'en' };
    const bo = this.bo;
    if (bo.type === 'node') {
      const nodeSO = await NodeSO.initMaybeNull(bo.nodeId);
      if (nodeSO) {
        return this.toNodeTalkVO(nodeSO, locale);
      }
      return null;
    }

    if (bo.type === 'expert') {
      return this.toExpertVOWithReddot();
    }

    if (bo.type === 'unit') {
      const aiNode = await AINodeSO.initMaybeNull(bo.unitId);
      return this.toUnitTalkVO(aiNode, locale);
    }
    throw new Error('not implemented');
  }

  /**
   * 根据 talk ID 和接收者信息更新置顶状态
   */
  static async updatePinnedById(
    talkId: string,
    recipient: RecipientProps,
    isPinned: boolean,
    userId: string,
  ): Promise<TalkSO | null> {
    const feedPO = await db.mongo.talk(recipient.recipientId).findOneAndUpdate(
      {
        id: talkId,
        'recipient.recipientType': recipient.recipientType,
        'recipient.recipientId': recipient.recipientId,
      },
      {
        $set: {
          isPinned,
          updatedAt: new Date(),
          updatedBy: userId,
        },
      },
      {
        new: true, // 返回更新后的文档
      },
    );

    if (!feedPO) {
      return null;
    }

    const talkSO = new TalkSO(feedPO);

    // 发送 SSE 事件通知客户端
    const feedVO = await talkSO.toVO();
    SseSO.emit(userId, { name: 'talk', talk: feedVO!, memberId: recipient.recipientId });

    return talkSO;
  }

  /**
   * 根据 talk ID 和接收者信息移除数据
   */
  static async removeById(
    talkId: string,
    recipient: RecipientProps,
    userId: string,
  ): Promise<boolean> {
    const result = await db.mongo.talk(recipient.recipientId).findOneAndDelete({
      id: talkId,
      'recipient.recipientType': recipient.recipientType,
      'recipient.recipientId': recipient.recipientId,
    });

    if (result) {
      // 发送 SSE 事件通知客户端数据已被删除
      // SseSO.emit(userId, {
      //   name: 'talk-removed',
      //   talkId,
      // });

      return true;
    }

    return false;
  }

  private static async recentlySOs(memberId: string): Promise<TalkSO[]> {
    const feeds = await TalkSO.list({ recipientType: 'MEMBER', recipientId: memberId });
    return feeds;
  }

  static async recently(userId: string, spaceId: string): Promise<TalkDetailVO[]> {
    const user = await UserSO.init(userId);
    const member = await user.getMember(spaceId);
    const spaceSO = await member.getSpace();
    const spaceMySO = member.getMy(spaceSO);
    const reddots = await spaceMySO.getReddots();

    const feeds = await TalkSO.recentlySOs(member.id);
    const feedNodeIds: Set<string> = new Set();
    for (const feed of feeds) {
      if (feed.talkType === 'node' && feed.nodeId) {
        feedNodeIds.add(feed.nodeId);
      }
    }

    const nodes = await NodeSO.findByIds(Array.from(feedNodeIds));
    const nodeMap = new Map(nodes.map((node) => [node.id, node]));

    const feedsVOs: TalkDetailVO[] = [];
    for (const feed of feeds) {
      if (feed.talkType === 'node' && feed.nodeId) {
        const node = nodeMap.get(feed.nodeId);
        if (node) {
          feedsVOs.push(await feed.toNodeTalkVO(node, user.locale));
        }
      }
      if (feed.talkType === 'expert') {
        const vo: TalkDetailVO = await feed.toExpertVOWithReddot(reddots);
        feedsVOs.push(vo);
      }
    }

    return feedsVOs;
  }

  // 空间站首页，TalkVO, 注意，这里是虚构的持久化获取的 TalkVO，绕开 DB，算法如下
  // 先填充 agent builder 和 super agent
  // 再填充最近的对话 Chat Talks 里的 Agents 节点
  // 如果数量小于 10，则填充空间站 ai 节点
  static async spaceHome(userId: string, spaceId: string): Promise<TalkDetailVO[]> {
    const user = await UserSO.init(userId);
    const member = await user.getMember(spaceId);

    const talks: TalkSO[] = [];
    const mockAgenBuilder = new TalkSO({
      id: 'mock-agent-builder',
      data: {
        type: 'expert',
        expertKey: 'builder',
      } as TalkBO,
      createdAt: new Date(),
      updatedAt: new Date(),
      isPinned: false,
    });
    const mockSpaceAgent = new TalkSO({
      id: 'mock-space-agent',
      data: {
        type: 'expert',
        expertKey: 'supervisor',
      } as TalkBO,
      createdAt: new Date(),
      updatedAt: new Date(),
      isPinned: false,
    });
    talks.push(mockAgenBuilder);
    talks.push(mockSpaceAgent);

    const recently = await TalkSO.recentlySOs(member.id);
    // 找出 recently 里的 agent 节点，并塞入 talk
    const recentlyAgentNodeIds: string[] = [];
    for (const talk of recently) {
      if (talk.bo.type === 'node' && talk.bo.nodeType === 'AI') {
        talks.push(talk);
        recentlyAgentNodeIds.push(talk.bo.nodeId);
      }
    }
    const SHOW_SIZE = 10;
    if (talks.length < SHOW_SIZE) {
      // 填充空间站节点
      const nodes = await NodeController.searchResource(user, {
        spaceId,
        resourceType: 'AI',
        scope: 'SPACE',
        pageNo: 1,
        pageSize: SHOW_SIZE - talks.length,
        keyword: '',
      });
      for (const no of nodes) {
        if (recentlyAgentNodeIds.includes(no.id)) {
          continue;
        }
        const talk = new TalkSO({
          id: no.id,
          data: {
            type: 'node',
            nodeId: no.id,
            nodeType: no.type,
          } as TalkBO,
          createdAt: new Date(),
          updatedAt: new Date(),
          isPinned: false,
        });
        talks.push(talk);
      }
    }

    const ret = await Promise.all(talks.map((talk) => talk.toVO()));
    return _.compact(ret);
  }

  static async orgChart(userId: string, unitIds: string[]): Promise<TalkDetailVO[]> {
    const user = await UserSO.init(userId);
    const members = await MemberSO.getMembersByIds(unitIds);

    const aiNodeIds = members.filter((member) => member.isAI).map((member) => member.userId);
    const aiNodes: AINodeSO[] = await AINodeSO.getByIds(aiNodeIds);
    const aiNodeMap = new Map(aiNodes.map((aiNode: AINodeSO) => [aiNode.id, aiNode]));

    const talks: TalkDetailVO[] = [];
    for (const member of members) {
      const talk = new TalkSO({
        id: member.id,
        data: {
          type: 'unit',
          unitId: member.id,
        } as TalkBO,
        createdAt: new Date(),
        updatedAt: new Date(),
        isPinned: false,
      });

      const vo = await talk.toUnitTalkVO(
        member.isAI ? (aiNodeMap.get(member.userId) ?? null) : null,
        user.locale,
      );
      talks.push(vo);
    }
    return talks;
  }
}
