import { db } from '@bika/server-orm';
import { SearchIndexTypes } from '@bika/server-orm/search';
import type { RecordListDTO } from '@bika/types/database/dto';
import type { DatabaseVO, RecordPaginationVO, ViewFieldVO, ViewVO } from '@bika/types/database/vo';
import { z } from 'zod';
import { AISearchSO } from '../../ai/server/ai-search-so';

export const AdminSearchPageTypes = ['INDICES', ...SearchIndexTypes] as const;
export const AdminSearchPageTypeSchema = z.enum(AdminSearchPageTypes);
export type AdminSearchPageType = z.infer<typeof AdminSearchPageTypeSchema>;

export class AdminSearchSO {
  /**
   * 获取某个类型的调查问卷，并转换为ViewVO
   *
   * 方法是任意取一个问卷，然后将其data字段的key作为columns
   */
  static getView(type: AdminSearchPageType): ViewVO {
    const columns: ViewFieldVO[] = [
      {
        id: 'index',
        databaseId: 'admin-search',
        type: 'LONG_TEXT',
        name: 'Index',
        primary: true,
        width: 300,
      },
      {
        id: 'count',
        databaseId: 'admin-search',
        type: 'NUMBER',
        name: 'Count',
        primary: false,
        property: {},
      },
      {
        id: 'url',
        databaseId: 'admin-search',
        type: 'URL',
        name: 'Url',
        primary: false,
        hidden: 'AI_PUBLISHED_CHAT' !== type,
      },
    ];

    return {
      id: type,
      name: type,
      type: 'TABLE',
      databaseId: 'admin-search',
      columns,
    };
  }

  public static async getRecords(dto: RecordListDTO): Promise<RecordPaginationVO> {
    const skip = dto.startRow;
    const take = dto.endRow - dto.startRow;
    const pageType: AdminSearchPageType = dto.viewId! as AdminSearchPageType;
    if (pageType === 'INDICES') {
      const indices = await db.search.indices();
      const total = indices.length;

      const records = [];

      for (const po of indices) {
        records.push({
          id: po.uuid!,
          databaseId: 'admin-search',
          revision: 1,
          cells: {
            index: {
              id: 'index',
              name: 'index',
              data: po.index,
              value: po.index,
            },
            count: {
              id: 'count',
              name: 'count',
              data: po['docs.count'],
              value: po['docs.count'],
            },
          },
        });
      }

      return {
        total,
        rows: records,
      };
    }
    if (pageType === 'AI_PUBLISHED_CHAT') {
      const chats = await AISearchSO.searchPublishedAIChats({
        pageNo: skip === 0 ? 1 : Math.floor(skip / take),
        pageSize: take,
      });
      return {
        total: chats.pagination.total,
        rows: chats.data.map((i) => {
          return {
            id: i.id,
            databaseId: 'admin-search',
            revision: 1,
            cells: {
              index: {
                id: 'index',
                name: 'index',
                data: `[${i.title}] ${i.description}`,
                value: `[${i.title}] ${i.description}`,
              },
              count: {
                id: 'count',
                name: 'count',
                data: 1,
                value: 1,
              },
              url: {
                id: 'url',
                name: 'url',
                data: `${process.env.APP_HOSTNAME}/share/${i.share?.id}`,
                value: `${process.env.APP_HOSTNAME}/share/${i.share?.id}`,
              },
            },
          };
        }),
      };
    }

    return {
      total: 0,
      rows: [],
    };

    // const filter: Record<string, string> = {};
    // if (dto.viewId === 'used') {
    //   filter.status = 'USED';
    // }
  }

  public static async getDatabase(): Promise<DatabaseVO> {
    return {
      id: 'survey',
      name: 'AI搜索索引治理',
      spaceId: 'admin',
      views: AdminSearchPageTypes.map((pageType) => AdminSearchSO.getView(pageType)),
    };
  }
}
