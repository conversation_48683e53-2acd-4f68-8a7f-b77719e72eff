import { getIntegrationTypesConfig } from '@bika/contents/config/client/integration/integrations';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { Integration } from '@bika/types/integration/bo';
import type { IntegrationVO } from '@bika/types/integration/vo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { SurveyInput } from '@bika/ui/admin/types-form/survey-input';
import { useCssColor } from '@bika/ui/colors';
import GotoOutlined from '@bika/ui/icons/components/goto_outlined';
import { Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/text-components';
import Link from '@mui/joy/Link';
import React, { useMemo } from 'react';
import { AdvertiseIntegrationBoInput } from './advertise-bo-input';
import { OpenAIIIntegrationBoInput } from './ai-model-bo-input';
import { AirtableIntegrationBoInput } from './airtable-bo-input';
import { AitableIntegrationBoInput } from './aitable-bo-input';
import { ApitableIntegrationBoInput } from './apitable-bo-input';
import { AwsOcrIntegrationBoInput } from './aws-ocr-bo-input';
import { DingtalkIntegrationBoInput } from './dingtalk-integration-bo-input';
import { FeishuIntegrationBoInput } from './feishu-bo-input';
import { ImapIntegrationBoInput } from './imap-bo-input';
import { MySQLIntegrationBoInput } from './mysql-bo-input';
import { PostgreSQLIntegrationBoInput } from './postgresql-bo-input';
import { SlackIntegrationBoInput } from './slack-bo-input';
import { SmtpIntegrationBoInput } from './smtp-bo-input';
import { TelegramIntegrationBoInput } from './telegram-bo-input';
import { TwitterIntegrationBoInput } from './twitter-bo-input';
import { TwitterOAuth1aIntegrationBoInput } from './twitter-oauth-1a-bo-input';
import { VikaIntegrationBoInput } from './vika-bo-input';
import { WebhookIntegrationBoInput } from './webhook-bo-input';
import { WecomIntegrationBoInput } from './wecom-bo-input';

interface Props {
  value: Integration;
  onChange: (newVal: Integration) => void;
  otherActions?: IntegrationVO[];
  locale: ILocaleContext;
  api: INodeResourceApi;
}

export function IntegrationBOInput(props: Props) {
  const { value, locale } = props;
  const { t, i } = locale;

  const colors = useCssColor();

  const setValue = props.onChange;

  const integrationsConfigs = useMemo(() => getIntegrationTypesConfig(locale), [locale]);

  const config = integrationsConfigs[props.value.type];

  const RenderType = () => {
    switch (value.type) {
      case 'SMTP_EMAIL_ACCOUNT':
        return <SmtpIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'TWITTER':
        return <TwitterIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'TWITTER_OAUTH_1A':
        return (
          <TwitterOAuth1aIntegrationBoInput value={value} onChange={setValue} locale={locale} />
        );
      case 'WE_COM':
        return <WecomIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'DING_TALK':
        return <DingtalkIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'TELEGRAM':
        return <TelegramIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'FEI_SHU':
        return <FeishuIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'SLACK':
        return <SlackIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'WEBHOOK':
        return <WebhookIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'AZURE_AI':
      case 'AMAZON_BEDROCK':
      case 'DEEPSEEK':
        return (
          <OpenAIIIntegrationBoInput
            value={value}
            onChange={(newValue) => {
              setValue({
                ...newValue,
                type: value.type,
              });
            }}
            locale={locale}
            strings={t.integration.deepseek}
          />
        );
      case 'OPENAI':
        return (
          <OpenAIIIntegrationBoInput
            value={value}
            onChange={(newValue) => {
              setValue({
                ...newValue,
                type: 'OPENAI',
              });
            }}
            locale={locale}
            strings={t.integration.openai}
          />
        );
      case 'AIRTABLE':
        return <AirtableIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'APITABLE':
        return <ApitableIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'VIKA':
        return <VikaIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'AITABLE':
        return <AitableIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'IMAP_EMAIL_ACCOUNT':
        return <ImapIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'POSTGRESQL':
        return <PostgreSQLIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'MYSQL':
        return <MySQLIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'AWS_OCR':
        return <AwsOcrIntegrationBoInput value={value} onChange={setValue} locale={locale} />;
      case 'ZOOM':
      case 'LINKEDIN':
      case 'MAKE_COM':
      case 'ZAPIER':
      case 'SIRI':
      case 'GITHUB':
      case 'GOOGLE':
      case 'WECHAT':
      case 'ALICLOUD_TONGYI':
      case 'GOOGLE_AI':
      case 'TENCENT_HUNYUAN':
      case 'BYTEDANCE_DOUBAO':
        return <AdvertiseIntegrationBoInput value={value} locale={locale} />;
      case 'HARDWARE_DEVICE':
        return null;
      default:
        return <SurveyInput surveyType={'COMING_SOON_AUTOMATION_ACTION'} />;
    }
  };

  return (
    <Box>
      <Box
        sx={{
          backgroundColor: 'var(--bg-popup)',
          borderRadius: 8,
          display: 'flex',
          width: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          className="flex items-center justify-center flex-col"
          sx={{
            gap: 2,
            p: 4,
          }}
        >
          <Box
            className="flex items-center"
            sx={{
              gap: 4,
            }}
          >
            <img
              src={config.displayConfig.icon}
              alt={config.displayConfig.label}
              width={48}
              height={48}
              className="rounded"
            />
            <img
              src="/assets/integrations/left-right.png"
              alt={config.displayConfig.label}
              width={36}
              height={36}
              className="rounded"
            />
            <img
              src="/assets/icons/logo/bika-logo-icon.png"
              alt={config.displayConfig.label}
              width={48}
              height={48}
              className="rounded"
            />
          </Box>
          <Typography level="h5">
            {t('integration.advertise.connect_to', { name: config.displayConfig.label })}
          </Typography>
          <Typography level="b2" textAlign="center" width="70%">
            {config.advertisementConfig?.description}
          </Typography>
          {config.displayConfig.links?.map((link, index) => (
            <Link
              key={index}
              href={link.url}
              target="_blank"
              endDecorator={<GotoOutlined color={colors.textLinkDefault} />}
            >
              <Typography level="b2">
                {i(link.text) || t.integration.description_know_more}
              </Typography>
            </Link>
          ))}
        </Box>
      </Box>
      <>{RenderType()}</>
    </Box>
  );
}
