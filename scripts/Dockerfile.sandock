# Sandock Dockerfile
# Multi-stage build for apps/sandock (Next.js only; docs framework TBD)

FROM node:22-bookworm AS base

RUN apt update && apt install -y rsync build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev python3 python3-venv python3-pip

# enable pnpm
RUN corepack enable pnpm && corepack prepare pnpm@latest-10 --activate
RUN npm install -g bun

# -------------------------
# Stage: init-builder (install shared workspace deps once)
# -------------------------
FROM base AS init-builder
WORKDIR /app

COPY ./package.json ./package.json
COPY ./pnpm-lock.yaml ./pnpm-lock.yaml
COPY ./pnpm-workspace.yaml ./pnpm-workspace.yaml

# Copy only packages needed for dependency resolution (reduce context)
COPY ./packages/sharelib ./packages/sharelib
COPY ./projects/basenext ./projects/basenext
COPY ./packages/sandock-domains ./packages/sandock-domains

COPY ./apps/sandock/package.json ./apps/sandock/package.json

RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
	export PUPPETEER_SKIP_DOWNLOAD=true && export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true && \
	pnpm install --frozen-lockfile

# -------------------------
# Stage: builder (build sandock app)
# -------------------------
FROM base AS builder
WORKDIR /app

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NEXT_TELEMETRY_DISABLED=1

COPY . .

# Load env if exists (non-fatal)
SHELL ["/bin/bash", "-c"]
RUN [ -f apps/sandock/.env.local ] && source apps/sandock/.env.local || echo "no local env"

# Install dependencies just for sandock app to leverage cache
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
	cd apps/sandock && pnpm install --frozen-lockfile

RUN cd packages/sandock-domains && pnpm run db:gen
# Build Next.js app
RUN cd apps/sandock && pnpm run build

# -------------------------
# Stage: runner (production)
# -------------------------
FROM base AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs && adduser --system --uid 1001 nextjs

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Copy workspace (dependencies) from init-builder
COPY --from=init-builder /app /app

# Copy standalone output from builder (if sandock next.config sets standalone in future). For now copy .next and public.
COPY --from=builder --chown=nextjs:nodejs /app/apps/sandock/.next /app/apps/sandock/.next
COPY --from=builder --chown=nextjs:nodejs /app/apps/sandock/public /app/apps/sandock/public

# Expose Sandock port (uses 3030 per package.json)
EXPOSE 3030
ENV PORT=3030
ENV HOSTNAME 0.0.0.0

USER nextjs

WORKDIR /app/apps/sandock
CMD ["node", "node_modules/next/dist/bin/next", "start", "-p", "3030"]

