import type { MiddlewareState } from '@floating-ui/dom';
import {
  arrow,
  autoUpdate,
  type DetectOverflowOptions,
  FloatingArrow,
  FloatingFocusManager,
  FloatingPortal,
  flip,
  offset,
  type Placement,
  safePolygon,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useHover,
  useId,
  useInteractions,
  useListNavigation,
  useMergeRefs,
  useRole,
} from '@floating-ui/react';
import * as React from 'react';

const CONST_SELECT_Z_INDEX = 999;

export const setIndex = (zIndex: number | string) => ({
  name: 'setIndexPlugin',
  fn(state: MiddlewareState) {
    Object.assign(state.elements.floating.style, {
      zIndex,
    });
    return {};
  },
});

type OffsetOptions =
  | number
  | {
      mainAxis?: number;
      crossAxis?: number;
      alignmentAxis?: number | null;
    };

interface PopoverOptions {
  initialOpen?: boolean;
  placement?: Placement;
  modal?: boolean;
  open?: boolean;
  zIndex?: number | string;
  clickEnable?: boolean;
  hoverEnable?: boolean;
  matchWidth?: boolean;
  showArrow?: boolean;
  onOpenChange?: (open: boolean) => void;
  options?: {
    offset?: OffsetOptions;
    flip?: DetectOverflowOptions;
    shift?: DetectOverflowOptions;
  };
  hoverOptions?: {
    delay?: number | { open?: number; close?: number };
    restMs?: number;
    buffer?: number;
  };
}

interface PopoverWithListNavigationOptions extends PopoverOptions {
  listNavigation?: {
    loop?: boolean;
    virtual?: boolean;
    orientation?: 'vertical' | 'horizontal' | 'both';
    focusItemOnOpen?: 'auto' | boolean;
    focusItemOnHover?: boolean;
    disabledIndices?: Array<number>;
  };
}

export function usePopover({
  initialOpen = false,
  placement = 'bottom',
  modal,
  open: controlledOpen,
  clickEnable,
  hoverEnable,
  zIndex,
  matchWidth = false,
  showArrow = false,
  options,
  hoverOptions,
  onOpenChange: setControlledOpen,
}: PopoverOptions = {}) {
  const [uncontrolledOpen, setUncontrolledOpen] = React.useState(initialOpen);
  const [labelId, setLabelId] = React.useState<string | undefined>();
  const [descriptionId, setDescriptionId] = React.useState<string | undefined>();
  const arrowRef = React.useRef<SVGSVGElement>(null);

  const open = controlledOpen ?? uncontrolledOpen;
  const setOpen = setControlledOpen ?? setUncontrolledOpen;

  const data = useFloating({
    placement,
    open,
    onOpenChange: setOpen,
    whileElementsMounted: autoUpdate,
    middleware: [
      setIndex(zIndex ?? CONST_SELECT_Z_INDEX),
      offset(options?.offset ?? 5),
      flip(
        options?.flip ?? {
          padding: 5,
        },
      ),
      shift(options?.shift ?? { padding: 5 }),
      ...(showArrow ? [arrow({ element: arrowRef })] : []),
      ...(matchWidth
        ? [
            size({
              apply({ rects, elements }) {
                Object.assign(elements.floating.style, {
                  width: `${rects.reference.width}px`,
                });
              },
            }),
          ]
        : []),
    ],
  });

  const { context } = data;

  const click = useClick(context, {
    enabled: clickEnable ?? controlledOpen == null,
  });
  const hover = useHover(context, {
    enabled: hoverEnable ?? false,
    delay: hoverOptions?.delay ?? { open: 0, close: 300 },
    restMs: hoverOptions?.restMs ?? 150,
    handleClose: safePolygon({
      buffer: hoverOptions?.buffer ?? 0.5,
    }),
  });
  const dismiss = useDismiss(context);
  const role = useRole(context);

  const interactions = useInteractions([click, hover, dismiss, role]);

  return React.useMemo(
    () => ({
      open,
      setOpen,
      ...interactions,
      ...data,
      modal,
      labelId,
      descriptionId,
      setLabelId,
      setDescriptionId,
      showArrow,
      arrowRef,
    }),
    [open, setOpen, interactions, data, modal, labelId, descriptionId, showArrow],
  );
}

export function usePopoverWithListNavigation({
  initialOpen = false,
  placement = 'bottom',
  modal,
  open: controlledOpen,
  clickEnable,
  hoverEnable,
  zIndex,
  matchWidth = false,
  showArrow = false,
  options,
  hoverOptions,
  onOpenChange: setControlledOpen,
  listNavigation,
}: PopoverWithListNavigationOptions = {}) {
  const [uncontrolledOpen, setUncontrolledOpen] = React.useState(initialOpen);
  const [labelId, setLabelId] = React.useState<string | undefined>();
  const [descriptionId, setDescriptionId] = React.useState<string | undefined>();
  const [activeIndex, setActiveIndex] = React.useState<number | null>(null);
  const listRef = React.useRef<Array<HTMLElement | null>>([]);
  const arrowRef = React.useRef<SVGSVGElement>(null);

  const open = controlledOpen ?? uncontrolledOpen;
  const setOpen = setControlledOpen ?? setUncontrolledOpen;

  const data = useFloating({
    placement,
    open,
    onOpenChange: (newOpen) => {
      setOpen(newOpen);
      if (!newOpen) {
        setActiveIndex(null);
      }
    },
    whileElementsMounted: autoUpdate,
    middleware: [
      setIndex(zIndex ?? CONST_SELECT_Z_INDEX),
      offset(options?.offset ?? 5),
      flip(
        options?.flip ?? {
          padding: 5,
        },
      ),
      shift(options?.shift ?? { padding: 5 }),
      ...(showArrow ? [arrow({ element: arrowRef })] : []),
      ...(matchWidth
        ? [
            size({
              apply({ rects, elements }) {
                Object.assign(elements.floating.style, {
                  width: `${rects.reference.width}px`,
                });
              },
            }),
          ]
        : []),
    ],
  });

  const { context } = data;

  const click = useClick(context, {
    enabled: clickEnable ?? controlledOpen == null,
  });
  const hover = useHover(context, {
    enabled: hoverEnable ?? false,
    delay: hoverOptions?.delay ?? { open: 0, close: 300 },
    restMs: hoverOptions?.restMs ?? 150,
  });
  const dismiss = useDismiss(context);
  const role = useRole(context);

  const listNavigationHook = useListNavigation(context, {
    listRef,
    activeIndex: activeIndex ?? null,
    onNavigate: setActiveIndex,
    loop: listNavigation?.loop ?? true,
    virtual: listNavigation?.virtual ?? false,
    orientation: listNavigation?.orientation ?? 'vertical',
    focusItemOnOpen: listNavigation?.focusItemOnOpen ?? 'auto',
    focusItemOnHover: listNavigation?.focusItemOnHover ?? true,
    disabledIndices: listNavigation?.disabledIndices,
  });

  const interactions = useInteractions([click, hover, dismiss, role, listNavigationHook]);

  return React.useMemo(
    () => ({
      open,
      setOpen,
      ...interactions,
      ...data,
      modal,
      labelId,
      descriptionId,
      setLabelId,
      setDescriptionId,
      activeIndex,
      setActiveIndex,
      listRef,
      showArrow,
      arrowRef,
    }),
    [open, setOpen, interactions, data, modal, labelId, descriptionId, activeIndex, showArrow],
  );
}

type ContextType =
  | (ReturnType<typeof usePopover> & {
      setLabelId: React.Dispatch<React.SetStateAction<string | undefined>>;
      setDescriptionId: React.Dispatch<React.SetStateAction<string | undefined>>;
    })
  | null;

type ContextWithListNavigationType =
  | (ReturnType<typeof usePopoverWithListNavigation> & {
      setLabelId: React.Dispatch<React.SetStateAction<string | undefined>>;
      setDescriptionId: React.Dispatch<React.SetStateAction<string | undefined>>;
    })
  | null;

const PopoverContext = React.createContext<ContextType>(null);
const PopoverWithListNavigationContext = React.createContext<ContextWithListNavigationType>(null);

export const usePopoverContext = () => {
  const context = React.useContext(PopoverContext);

  if (context == null) {
    throw new Error('Popover components must be wrapped in <Popover />');
  }

  return context;
};

export const usePopoverWithListNavigationContext = () => {
  const context = React.useContext(PopoverWithListNavigationContext);

  if (context == null) {
    throw new Error(
      'PopoverWithListNavigation components must be wrapped in <PopoverWithListNavigation />',
    );
  }

  return context;
};

export function Popover({
  children,
  modal = false,
  ...restOptions
}: {
  children: React.ReactNode;
} & PopoverOptions) {
  // This can accept any props as options, e.g. `placement`,
  // or other positioning options.
  const popover = usePopover({ modal, ...restOptions });
  return <PopoverContext.Provider value={popover}>{children}</PopoverContext.Provider>;
}

export function PopoverWithListNavigation({
  children,
  modal = false,
  ...restOptions
}: {
  children: React.ReactNode;
} & PopoverWithListNavigationOptions) {
  const popover = usePopoverWithListNavigation({ modal, ...restOptions });
  return (
    <PopoverWithListNavigationContext.Provider value={popover}>
      {children}
    </PopoverWithListNavigationContext.Provider>
  );
}

interface PopoverTriggerProps {
  children: React.ReactNode;
  asChild?: boolean;
}

export const PopoverTrigger = React.forwardRef<
  HTMLElement,
  React.HTMLProps<HTMLElement> & PopoverTriggerProps
>(({ children, asChild = false, ...props }, propRef) => {
  const context = usePopoverContext();
  const childrenRef = (children as any).ref;
  const ref = useMergeRefs([context.refs.setReference, propRef, childrenRef]);

  // `asChild` allows the user to pass any element as the anchor
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(
      children,
      context.getReferenceProps({
        ref,
        ...props,
        ...children.props,
        'data-state': context.open ? 'open' : 'closed',
      }),
    );
  }

  return (
    <button
      style={{
        background: 'none',
        border: 'none',
        cursor: 'pointer',
        width: '100%',
      }}
      className={'bg-none'}
      ref={ref}
      type="button"
      // The user can style the trigger based on the state
      data-state={context.open ? 'open' : 'closed'}
      {...context.getReferenceProps(props)}
    >
      {children}
    </button>
  );
});

export const PopoverWithListNavigationTrigger = React.forwardRef<
  HTMLElement,
  React.HTMLProps<HTMLElement> & PopoverTriggerProps
>(({ children, asChild = false, ...props }, propRef) => {
  const context = usePopoverWithListNavigationContext();
  const childrenRef = (children as any).ref;
  const ref = useMergeRefs([context.refs.setReference, propRef, childrenRef]);

  // `asChild` allows the user to pass any element as the anchor
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(
      children,
      context.getReferenceProps({
        ref,
        ...props,
        ...children.props,
        'data-state': context.open ? 'open' : 'closed',
      }),
    );
  }

  return (
    <button
      style={{
        background: 'none',
        border: 'none',
        cursor: 'pointer',
        width: '100%',
      }}
      className={'bg-none'}
      ref={ref}
      type="button"
      // The user can style the trigger based on the state
      data-state={context.open ? 'open' : 'closed'}
      {...context.getReferenceProps(props)}
    >
      {children}
    </button>
  );
});

export const PopoverContent = React.forwardRef<HTMLDivElement, React.HTMLProps<HTMLDivElement>>(
  ({ style, ...props }, propRef) => {
    const { context: floatingContext, ...context } = usePopoverContext();
    const ref = useMergeRefs([context.refs.setFloating, propRef]);

    if (!floatingContext.open) return null;

    return (
      <FloatingPortal>
        <FloatingFocusManager
          context={floatingContext}
          modal={context.modal}
          closeOnFocusOut={false}
        >
          <div
            ref={ref}
            style={{ ...context.floatingStyles, ...style }}
            // Removed aria-labelledby/aria-describedby to satisfy rule; handled by role props
            {...context.getFloatingProps(props)}
          >
            {context.showArrow && (
              <FloatingArrow
                ref={context.arrowRef}
                context={floatingContext}
                fill="var(--bg-popup)"
                stroke="var(--border-default)"
                strokeWidth={1}
                width={8}
                height={8}
              />
            )}
            {props.children}
          </div>
        </FloatingFocusManager>
      </FloatingPortal>
    );
  },
);

export const PopoverWithListNavigationContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLProps<HTMLDivElement>
>(({ style, ...props }, propRef) => {
  const { context: floatingContext, ...context } = usePopoverWithListNavigationContext();
  const ref = useMergeRefs([context.refs.setFloating, propRef]);

  if (!floatingContext.open) return null;

  return (
    <FloatingPortal>
      <FloatingFocusManager
        context={floatingContext}
        modal={context.modal}
        closeOnFocusOut={false}
        initialFocus={-1}
      >
        <div
          ref={ref}
          style={{ ...context.floatingStyles, ...style }}
          {...context.getFloatingProps(props)}
        >
          {context.showArrow && (
            <FloatingArrow
              ref={context.arrowRef}
              context={floatingContext}
              fill="var(--bg-popup)"
              stroke="var(--border-default)"
              strokeWidth={1}
              width={8}
              height={8}
            />
          )}
          {props.children}
        </div>
      </FloatingFocusManager>
    </FloatingPortal>
  );
});

export const PopoverHeading = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLProps<HTMLHeadingElement>
>((props, ref) => {
  const { setLabelId } = usePopoverContext();
  const id = useId();

  // Only sets `aria-labelledby` on the Popover root element
  // if this component is mounted inside it.
  React.useLayoutEffect(() => {
    setLabelId(id);
    return () => setLabelId(undefined);
  }, [id, setLabelId]);

  return (
    <h2 {...props} ref={ref} id={id}>
      {props.children}
    </h2>
  );
});

export const PopoverDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLProps<HTMLParagraphElement>
>((props, ref) => {
  const { setDescriptionId } = usePopoverContext();
  const id = useId();

  // Only sets `aria-describedby` on the Popover root element
  // if this component is mounted inside it.
  React.useLayoutEffect(() => {
    setDescriptionId(id);
    return () => setDescriptionId(undefined);
  }, [id, setDescriptionId]);

  return <p {...props} ref={ref} id={id} />;
});

export const PopoverClose = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>((props, ref) => {
  const { setOpen } = usePopoverContext();
  return (
    <button
      type="button"
      ref={ref}
      {...props}
      onClick={(event) => {
        props.onClick?.(event);
        setOpen(false);
      }}
    />
  );
});
