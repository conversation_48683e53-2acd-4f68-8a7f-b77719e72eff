export async function directDownload(href: string, name?: string) {
  const a = document.createElement('a');
  const formatHref = href.startsWith('http') ? href : `${window.location.origin}${href}`;

  try {
    const response = await fetch(formatHref);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = name || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the object URL
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    const url = new URL(formatHref);
    url.searchParams.set('attname', name ?? 'Unknown');
    url.searchParams.set('download', '1');
    url.searchParams.set(
      'response-content-disposition',
      `attachment; filename="${name || 'Unknown.txt'}"`,
    );
    url.searchParams.set('response-content-type', 'application/octet-stream');

    const downloadUrl = url.toString();

    a.download = name ?? 'Unknown';
    a.href = downloadUrl;
    document.body.appendChild(a);
    a.click();
    a.remove();
  }
}
