import type {
  ActionTemplate,
  InputFieldBO,
  IntegrationTemplate,
  PackageDataBO,
  ToolApp,
} from '@toolsdk.ai/sdk-ts/types/bo';
import type { IPackageToolRunFullBody } from '../types';
import { strTemplate } from '../utils';
import { buildInputData } from './fields-helper';
import { FunctionsHelper } from './functions-helper';

export interface IPackageDataInterface {
  /**
   * 汇总的配置和工具列表。
   *
   * 一起获取配置和工具列表，特别是 npm 类型，分开获取会导致 npm 包被多次加载。
   */
  getToolApp(): Promise<ToolApp>;

  findConfiguration(): Promise<IntegrationTemplate | undefined>;

  listTools(): Promise<Record<string, ActionTemplate> | undefined>;

  runTool(body: IPackageToolRunFullBody, authData?: Record<string, unknown>): Promise<unknown>;
}

export abstract class PackageDataAdapter<T extends PackageDataBO = PackageDataBO>
  implements IPackageDataInterface
{
  protected _packageData: T;

  constructor(packageData: T) {
    this._packageData = packageData;
  }

  get packageData(): T {
    return this._packageData;
  }

  getReadme(): string {
    return '';
  }

  abstract getToolApp(): Promise<ToolApp>;

  async getUrl(): Promise<string | undefined> {
    return undefined;
  }

  async findConfiguration(): Promise<IntegrationTemplate | undefined> {
    const toolApp = await this.getToolApp();
    return toolApp.authentication;
  }

  async listTools(): Promise<Record<string, ActionTemplate> | undefined> {
    const toolApp = await this.getToolApp();
    return toolApp.tools;
  }

  async runTool(
    body: IPackageToolRunFullBody,
    authData?: Record<string, unknown>,
  ): Promise<unknown> {
    const toolKey = body.toolKey;

    const [configuration, tools] = await Promise.all([this.findConfiguration(), this.listTools()]);

    if (configuration && !authData) {
      throw new Error('Configuration is required to run tool.');
    }

    const tool = tools?.[toolKey];
    if (!tool) {
      throw new Error(`Tool ${toolKey} not found`);
    }
    const { inputFields: inputDynamicFields, perform } = tool.operation;

    // TODO: dynamic field process
    const inputFields = inputDynamicFields as InputFieldBO[];
    const inputData = this.buildInputData(inputFields, body);

    return FunctionsHelper.runPerform(perform, { inputData, authData });
  }

  renderValueWithContext(value: unknown, context: unknown = {}): unknown {
    if (value == null) {
      return value;
    }

    if (typeof value === 'string') {
      return strTemplate(value, context || {});
    }

    if (Array.isArray(value)) {
      return value.map((item) => this.renderValueWithContext(item, context));
    }

    if (typeof value === 'object' && value !== null) {
      return Object.fromEntries(
        Object.entries(value).map(([k, v]) => [k, this.renderValueWithContext(v, context)]),
      );
    }

    return value;
  }

  buildInputData(inputFields: InputFieldBO[], body: IPackageToolRunFullBody) {
    const { context, inputData: inputValues } = body;

    const template = (field: InputFieldBO, input: unknown) => {
      if (field.list) {
        if (!Array.isArray(input)) {
          throw new Error(`Field ${field.label} should be an array.`);
        }

        return input.map((val) => this.renderValueWithContext(val, context));
      }

      return this.renderValueWithContext(input, context);
    };

    return buildInputData(inputFields, inputValues, template);
  }
}
