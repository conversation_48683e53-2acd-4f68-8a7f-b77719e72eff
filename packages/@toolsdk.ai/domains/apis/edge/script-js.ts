import * as vm from 'node:vm';
import axios from 'axios';
import type { Context } from 'hono';
import lodash from 'lodash';
import fetch from 'node-fetch';

// 避免被 tree-shaking，以便eval能require
export const supportLibs = {
  fetch,
  axios,
  lodash,
  _: lodash,
};

// POST
export default async function handler(c: Context) {
  const edgeToken = process.env.EDGE_TOKEN!;
  const headerEdgeToken = c.req.header('edge-token');
  if (edgeToken !== headerEdgeToken) {
    return c.json({ error: 'Edge Token Auth fail' }, 500);
  }

  const body = await c.req.text();
  const forbiddenKeywords = [
    'arguments.callee.caller',
    'constructor',
    'proxy',
    'require',
    'process',
    'eval',
  ];
  for (const keyword of forbiddenKeywords) {
    // Use word boundary regex to match only whole words, not substrings
    // This prevents blocking words like "processing" when we only want to block "process"
    const regex = new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
    if (regex.test(body)) {
      console.log('script-js error with forbiddenKeyword ', keyword);
      return c.json({ err: `script illegal.` }, 500);
    }
  }
  try {
    const context = Object.create(null);
    const runResult = await vm.runInNewContext(body, { ...supportLibs, ...context });

    return c.json({
      script: body,
      return: runResult,
    });
  } catch (e: any) {
    console.error('script-js error', e);
    return c.json(
      {
        err: e.message,
      },
      500,
    );
  }
}
