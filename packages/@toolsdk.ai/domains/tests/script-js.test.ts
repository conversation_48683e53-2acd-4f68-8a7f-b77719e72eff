import { describe, expect, test } from 'vitest';

/**
 * Test for script-js forbidden keyword filtering
 * Issue: Sensitive words with letters or numbers before/after should not be restricted
 * 
 * Current behavior: "process" blocks "processing", "subprocess", etc.
 * Desired behavior: Only block exact whole words
 */

// Helper function to simulate the current forbidden keyword check
function currentKeywordCheck(body: string, keywords: string[]): string | null {
  for (const keyword of keywords) {
    if (body.includes(keyword)) {
      return keyword;
    }
  }
  return null;
}

// Helper function for the new word boundary-based check
function improvedKeywordCheck(body: string, keywords: string[]): string | null {
  // Escape regex special chars in a keyword
  const escapeRegExp = (s: string) => s.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\$&');

  let earliestIndex = Infinity;
  let earliestKeyword: string | null = null;

  for (const keyword of keywords) {
    // Use word boundary regex to match only whole words (case-insensitive)
    const regex = new RegExp(`\\b${escapeRegExp(keyword)}\\b`, 'i');
    const match = regex.exec(body);
    if (match && typeof match.index === 'number') {
      if (match.index < earliestIndex) {
        earliestIndex = match.index;
        earliestKeyword = keyword;
      }
    }
  }

  return earliestKeyword;
}

describe('Script-JS Forbidden Keyword Filtering', () => {
  const forbiddenKeywords = [
    'arguments.callee.caller',
    'constructor',
    'proxy',
    'require',
    'process',
    'eval',
  ];

  describe('Current implementation (problematic)', () => {
    test('should block forbidden keywords in isolation', () => {
      expect(currentKeywordCheck('process', forbiddenKeywords)).toBe('process');
      expect(currentKeywordCheck('eval', forbiddenKeywords)).toBe('eval');
      expect(currentKeywordCheck('require', forbiddenKeywords)).toBe('require');
    });

    test('should incorrectly block words containing forbidden keywords', () => {
      // These should NOT be blocked but currently are
      expect(currentKeywordCheck('processing', forbiddenKeywords)).toBe('process');
      expect(currentKeywordCheck('subprocess', forbiddenKeywords)).toBe('process');
      expect(currentKeywordCheck('evaluation', forbiddenKeywords)).toBe('eval');
      expect(currentKeywordCheck('medieval', forbiddenKeywords)).toBe('eval');
      expect(currentKeywordCheck('required', forbiddenKeywords)).toBe('require');
    });
  });

  describe('Improved implementation (desired behavior)', () => {
    test('should block forbidden keywords in isolation', () => {
      expect(improvedKeywordCheck('process', forbiddenKeywords)).toBe('process');
      expect(improvedKeywordCheck('eval', forbiddenKeywords)).toBe('eval');
      expect(improvedKeywordCheck('require', forbiddenKeywords)).toBe('require');
    });

    test('should block forbidden keywords with non-alphanumeric boundaries', () => {
      expect(improvedKeywordCheck('process()', forbiddenKeywords)).toBe('process');
      expect(improvedKeywordCheck('eval;', forbiddenKeywords)).toBe('eval');
      expect(improvedKeywordCheck('require.resolve', forbiddenKeywords)).toBe('require');
      expect(improvedKeywordCheck('(process)', forbiddenKeywords)).toBe('process');
      expect(improvedKeywordCheck(' eval ', forbiddenKeywords)).toBe('eval');
    });

    test('should NOT block words containing forbidden keywords as substrings', () => {
      // These should be allowed - words that contain forbidden keywords but have letters/digits before/after
      expect(improvedKeywordCheck('processing', forbiddenKeywords)).toBe(null);
      expect(improvedKeywordCheck('subprocess', forbiddenKeywords)).toBe(null);
      expect(improvedKeywordCheck('evaluation', forbiddenKeywords)).toBe(null);
      expect(improvedKeywordCheck('medieval', forbiddenKeywords)).toBe(null);
      expect(improvedKeywordCheck('required', forbiddenKeywords)).toBe(null);
      expect(improvedKeywordCheck('aprocess', forbiddenKeywords)).toBe(null);
      expect(improvedKeywordCheck('processb', forbiddenKeywords)).toBe(null);
    });

    test('should handle mixed case scenarios', () => {
      // Should still block regardless of case
      expect(improvedKeywordCheck('Process', forbiddenKeywords)).toBe('process');
      expect(improvedKeywordCheck('EVAL', forbiddenKeywords)).toBe('eval');
      
      // Should not block when part of other words
      expect(improvedKeywordCheck('Processing', forbiddenKeywords)).toBe(null);
      expect(improvedKeywordCheck('EVALUATION', forbiddenKeywords)).toBe(null);
    });

    test('should handle complex code scenarios', () => {
      // Valid JavaScript that uses forbidden keywords and should be blocked for security
      const codeWithForbiddenUsage = `
        const result = eval('1 + 1');
        const env = process.env;
        const dataProcessing = require('data-processing');
      `;
      
      // Should block this code due to standalone forbidden keywords
      expect(improvedKeywordCheck(codeWithForbiddenUsage, forbiddenKeywords)).toBe('eval');
      
      // Words containing forbidden keywords should be allowed
      expect(improvedKeywordCheck('processing evaluation required', forbiddenKeywords)).toBe(null);
    });

    test('should handle special keyword with dots', () => {
      // Special case for 'arguments.callee.caller'
      expect(improvedKeywordCheck('arguments.callee.caller', forbiddenKeywords)).toBe('arguments.callee.caller');
      expect(improvedKeywordCheck('arguments.callee.caller.toString()', forbiddenKeywords)).toBe('arguments.callee.caller');
      
      // Should not block partial matches
      expect(improvedKeywordCheck('arguments.callee', forbiddenKeywords)).toBe(null);
      expect(improvedKeywordCheck('arguments.callee.caller.extra', forbiddenKeywords)).toBe('arguments.callee.caller');
    });
  });
});