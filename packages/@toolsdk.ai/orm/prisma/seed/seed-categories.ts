import { getAppEnv } from 'sharelib/app-env';
import McpServerApiClient from '../../api/mcp-server-api-client';
import { db } from '../dao/db';

export async function seedPackageCategories(developerId: string) {
  if (getAppEnv() === 'SELF-HOSTED') {
    return;
  }

  const mcpClient = new McpServerApiClient();
  const jCats = await mcpClient.fetchCategoriesConfig();

  for (const cat of jCats) {
    await db.prisma.packageCategory.upsert({
      where: { key: cat.key },
      create: {
        id: cat.key, // Assuming key is unique and can be used as ID
        key: cat.key,
        name: cat.name,
        description: cat.description || '',
        createdBy: developerId,
        updatedBy: developerId,
        // icon: cat.icon || '',
        // order: cat.order || 0,
      },
      update: {
        name: cat.name,
        description: cat.description || '',
        createdBy: developerId,
        updatedBy: developerId,
        // icon: cat.icon || '',
        // order: cat.order || 0,
      },
    });
  }
}
