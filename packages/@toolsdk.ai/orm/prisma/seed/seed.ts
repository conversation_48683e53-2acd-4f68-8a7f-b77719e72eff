// 种子seed数据

import { db } from '../dao/db';
import { DeveloperDAO } from '../dao/developer-dao';
import { PrismaClient } from '../prisma-client';
import { seedPackageCategories } from './seed-categories';
import { seedMCPServers } from './seed-mcp-servers';
// import { seedN8N } from './seed-n8n';
import { seedPackageClients } from './seed-package-clients';
import { seedToolApps } from './seed-toolapp';
import { seedLocalZapierPackages } from './seed-zapier';

const prisma = new PrismaClient();

async function main() {
  await db.initMinioBucket();

  const superAdmin = await DeveloperDAO.upsertSuper();

  await seedPackageCategories(superAdmin.id);

  console.log('Upsert admin user', { superAdmin });

  const toolAppPackageFullKeys = await seedToolApps(superAdmin.id);

  const zapierPackageFullKeys = await seedLocalZapierPackages(superAdmin.id);
  await seedPackageClients(superAdmin.id);

  // @deprecated
  // await seedLocalFormAppActions(superAdmin.id);

  // CI 里不 seed mcp，太慢
  const mcpServerPackageFullKeys = await seedMCPServers(superAdmin.id, [
    ...toolAppPackageFullKeys,
    ...zapierPackageFullKeys,
  ]);

  // 检查是否有重复的package
  const allPackageFullKeys = [
    ...toolAppPackageFullKeys,
    ...zapierPackageFullKeys,
    ...mcpServerPackageFullKeys,
  ];
  const packageKeyCount: Record<string, number> = {};
  allPackageFullKeys.forEach((key) => {
    packageKeyCount[key] = (packageKeyCount[key] || 0) + 1;
  });
  const duplicateKeys = Object.keys(packageKeyCount).filter((key) => packageKeyCount[key] > 1);
  if (duplicateKeys.length > 0) {
    console.error('Duplicate package keys found:', duplicateKeys);
    throw new Error('Duplicate package keys found');
  }
}

main()
  .then(async () => {
    console.log('prisma.$disconnect()....');
    await prisma.$disconnect();
    console.log('Prisma disconnected');
    process.exit(0);
  })
  .catch(async (e) => {
    console.log('Catch prisma.$disconnect()....');
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
