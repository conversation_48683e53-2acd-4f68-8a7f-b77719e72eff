// Sandbox metrics instrumentation helpers

import { generateNanoID } from 'basenext/utils';
import { prisma } from '../prisma/db';
import type { Prisma, SandboxLifecycleEventType, SandboxStatus } from '../prisma/prisma-client';

// NOTE: Do not mutate status here; callers perform status updates and then call event helpers.
// Use Prisma enum SandboxLifecycleEventType directly now that migration + generate completed.

export async function recordStatusEvent(params: {
  sandboxId: string;
  fromStatus?: SandboxStatus | null;
  toStatus?: SandboxStatus | null;
  type?: SandboxLifecycleEventType; // default STATUS_CHANGE
  meta?: Record<string, unknown>;
  tx?: Prisma.TransactionClient;
}) {
  const { sandboxId, fromStatus, toStatus, type = 'STATUS_CHANGE', meta = {}, tx } = params;
  const client = tx ?? prisma;
  await client.sandboxLifecycleEvent.create({
    data: {
      id: generateNanoID('sde'),
      sandboxId,
      type,
      fromStatus: fromStatus || undefined,
      toStatus: toStatus || undefined,
      meta: meta as unknown as Prisma.InputJsonValue,
    },
  });
}

export async function openRunSegment(
  sandboxId: string,
  startAt = new Date(),
  tx?: Prisma.TransactionClient,
) {
  const client = tx ?? prisma;
  const open = await client.sandboxRunSegment.findFirst({ where: { sandboxId, endAt: null } });
  if (open) return open.id;
  const seg = await client.sandboxRunSegment.create({
    data: {
      id: generateNanoID('sde'),
      sandboxId,
      startAt,
    },
  });
  return seg.id;
}

export async function closeRunSegment(
  sandboxId: string,
  reason: string,
  when = new Date(),
  tx?: Prisma.TransactionClient,
) {
  const client = tx ?? prisma;
  const seg = await client.sandboxRunSegment.findFirst({ where: { sandboxId, endAt: null } });
  if (!seg) return;
  const durationMs = when.getTime() - seg.startAt.getTime();
  // Fetch sandbox row for limits (cpuLimit in milli-core? stored as Int) & memoryLimit (MiB)
  const sandbox = await client.sandbox.findUnique({ where: { id: sandboxId } });
  let cpuCoreMs = null as number | null;
  let memMiBSeconds = null as number | null;
  if (sandbox) {
    const cpuLimit = sandbox.cpuLimit; // Int? (assume milli-core or cores?) We treat as milli-cores if < 1000 else cores.
    const memoryLimit = sandbox.memoryLimit; // Int? assume MiB
    const durationSec = durationMs / 1000;
    if (cpuLimit != null) {
      // Heuristic(启发): if value <= 64 treat as cores; convert to milli-core.
      const milli = cpuLimit <= 64 ? cpuLimit * 1000 : cpuLimit; // allow already milli
      cpuCoreMs = Math.round((milli / 1000) * durationMs);
    }
    if (memoryLimit != null) {
      memMiBSeconds = Math.round(memoryLimit * durationSec);
    }
  }
  await client.sandboxRunSegment.update({
    where: { id: seg.id },
    data: {
      endAt: when,
      endReason: reason,
      durationMs,
      // @ts-ignore pending prisma generate (cpuCoreMs field)
      cpuCoreMs,
      // @ts-ignore pending prisma generate (memMiBSeconds field)
      memMiBSeconds,
    },
  });
  await client.sandbox.update({
    where: { id: sandboxId },
    data: {
      accumulatedRunMs: { increment: durationMs },
      // @ts-ignore pending prisma generate (accumulatedCpuCoreMs)
      accumulatedCpuCoreMs: cpuCoreMs != null ? { increment: cpuCoreMs } : undefined,
      // @ts-ignore pending prisma generate (accumulatedMemMiBSeconds)
      accumulatedMemMiBSeconds: memMiBSeconds != null ? { increment: memMiBSeconds } : undefined,
      lastStoppedAt: when,
    },
  });
  await client.sandboxLifecycleEvent.create({
    data: {
      id: generateNanoID('sde'),
      sandboxId,
      type: 'RUN_SEGMENT_END',
      meta: { reason, durationMs, cpuCoreMs, memMiBSeconds } as unknown as Prisma.InputJsonValue,
    },
  });
}

export async function recordStartSuccess(
  sandboxId: string,
  createdAt: Date,
  isRestart: boolean,
  tx?: Prisma.TransactionClient,
) {
  const client = tx ?? prisma;
  const now = new Date();
  const startupMs = now.getTime() - createdAt.getTime();
  await client.sandbox.update({
    where: { id: sandboxId },
    data: {
      firstStartedAt: isRestart ? undefined : now,
      lastStartedAt: now,
      lastStartupDurationMs: startupMs,
      statusChangedAt: now,
    },
  });
  await client.sandboxLifecycleEvent.create({
    data: {
      id: generateNanoID('sde'),
      sandboxId,
      type: 'START_SUCCESS',
      meta: { startupMs, restart: isRestart } as unknown as Prisma.InputJsonValue,
    },
  });
  await openRunSegment(sandboxId, now, client);
}
