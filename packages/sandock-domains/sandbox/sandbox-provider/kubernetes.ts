// Kubernetes based sandbox provider.
// Goals (minimal viable):
// 1. Create ephemeral Pod with single container for execution
// 2. Exec shell commands inside container
// 3. Simple file ops implemented via shell (cat/ls/rm) & base64 for write
// 4. Run code snippets by writing temp file then executing interpreter
// NOTE: This implementation intentionally avoids streaming and advanced copy APIs
// to keep dependency surface small. It uses dynamic import so library remains usable
// without k8s dependency until provider is instantiated.

/*
  NOTE: This file interacts with @kubernetes/client-node which lacks easy
  lightweight typings for dynamic runtime import. We avoid 'any' by using
  'unknown' and minimal internal helper types; remaining unavoidable casts
  are isolated in a few utility functions.
*/
import { randomUUID } from 'node:crypto';
import { PassThrough, Readable } from 'node:stream';
import { CoreV1Api, Exec, KubeConfig } from '@kubernetes/client-node';
import type {
  SandboxCodeOptions,
  SandboxExecutionResult,
  SandboxFsWriteOptions,
  SandboxProvider,
  SandboxShellOptions,
} from '../types';

export interface KubernetesSandboxOptions {
  namespace?: string; // target namespace (default: default)
  image?: string; // container image (default node:20-bullseye)
  workdir?: string; // working directory inside container (default /sandbox)
  memoryLimitMb?: number; // memory limit
  cpuLimit?: string; // cpu limit (e.g., '500m')
  serviceAccountName?: string; // optional SA
  podName?: string; // stable name (if omitted random)
  pullPolicy?: 'IfNotPresent' | 'Always';
  idleSeconds?: number; // pod sleep duration (to keep alive)
}

// Narrow interface subsets to keep dependency surface small
type IKubeConfig = KubeConfig;
type ICoreV1Api = CoreV1Api;

export class KubernetesSandboxProvider implements SandboxProvider {
  private opts: Required<
    Pick<
      KubernetesSandboxOptions,
      | 'namespace'
      | 'image'
      | 'workdir'
      | 'memoryLimitMb'
      | 'cpuLimit'
      | 'serviceAccountName'
      | 'pullPolicy'
      | 'idleSeconds'
    >
  > & { podName?: string };
  private podName?: string;
  private started = false;
  // Using unknown to avoid leaking 'any' while still allowing dynamic interaction without full type dep duplication
  private kc?: IKubeConfig;
  private core?: ICoreV1Api;

  constructor(options: KubernetesSandboxOptions = {}) {
    // Allow overriding via environment variables (prefixed to avoid collisions)
    // These are intentionally soft overrides (options param still has highest precedence)
    const env = process.env;
    const envNum = (key: string, d: number): number => {
      const v = env[key];
      if (!v) return d;
      const n = Number(v);
      return Number.isFinite(n) && n > 0 ? n : d;
    };
    this.opts = {
      namespace: options.namespace ?? env.SANDBOX_K8S_NAMESPACE ?? 'default',
      image: options.image ?? env.SANDBOX_K8S_IMAGE ?? 'node:20-bullseye',
      workdir: options.workdir ?? env.SANDBOX_K8S_WORKDIR ?? '/sandbox',
      memoryLimitMb: options.memoryLimitMb ?? envNum('SANDBOX_K8S_MEMORY_MB', 512),
      cpuLimit: options.cpuLimit ?? env.SANDBOX_K8S_CPU_LIMIT ?? '500m',
      serviceAccountName: options.serviceAccountName ?? env.SANDBOX_K8S_SERVICE_ACCOUNT ?? '',
      pullPolicy:
        options.pullPolicy ??
        (['Always', 'IfNotPresent'].includes(env.SANDBOX_K8S_PULL_POLICY || '')
          ? (env.SANDBOX_K8S_PULL_POLICY as 'Always' | 'IfNotPresent')
          : 'IfNotPresent'),
      idleSeconds: options.idleSeconds ?? envNum('SANDBOX_K8S_IDLE_SECONDS', 3600),
      podName: options.podName ?? (env.SANDBOX_K8S_POD_NAME || undefined),
    };
  }

  // cpuLimit may be forms like '500m' or '1', convert to cores number
  get cpu(): number {
    const v = this.opts.cpuLimit.trim();
    if (v.endsWith('m')) {
      const n = Number(v.slice(0, -1));
      return Number.isFinite(n) ? +(n / 1000).toFixed(3) : 0;
    }
    const n = Number(v);
    return Number.isFinite(n) ? n : 0;
  }
  get mem(): number {
    return this.opts.memoryLimitMb * 1024 * 1024;
  }

  private async loadClient() {
    if (this.kc) return;
    // Precedence: explicit in-cluster env -> explicit env server -> default kubeconfig
    const env = process.env;
    let kc: KubeConfig | undefined;
    try {
      if (env.SANDBOX_K8S_IN_CLUSTER === 'true') {
        kc = new KubeConfig();
        kc.loadFromCluster();
      } else if (env.SANDBOX_K8S_SERVER) {
        kc = this.buildKubeConfigFromEnv();
      } else {
        kc = new KubeConfig();
        kc.loadFromDefault();
      }
    } catch (e) {
      throw new Error(`Failed loading kubeconfig: ${(e as Error).message}`);
    }
    this.kc = kc;
    this.core = kc.makeApiClient(CoreV1Api);
    // Exec used directly when needed
  }

  /** Derive a conservative CPU request from limit (25% floor 50m). */
  private deriveCpuRequest(limit: string): string {
    const v = limit.trim();
    let cores: number;
    if (v.endsWith('m')) {
      const n = Number(v.slice(0, -1));
      cores = Number.isFinite(n) ? n / 1000 : 0.1;
    } else {
      const n = Number(v);
      cores = Number.isFinite(n) ? n : 0.1;
    }
    const req = Math.max(cores * 0.25, 0.05); // at least 0.05 cores
    const milli = Math.round(req * 1000);
    return milli < 1000 ? `${milli}m` : `${(milli / 1000).toFixed(3).replace(/\.000$/, '')}`;
  }

  /** Build a KubeConfig from SANDBOX_K8S_* env vars. Returns configured instance. */
  private buildKubeConfigFromEnv(): KubeConfig {
    const env = process.env;
    const server = env.SANDBOX_K8S_SERVER;
    if (!server) throw new Error('SANDBOX_K8S_SERVER required when using env configuration');
    const caData = env.SANDBOX_K8S_CA_DATA;
    const skipTLS = env.SANDBOX_K8S_SKIP_TLS_VERIFY === 'true';
    const token = env.SANDBOX_K8S_TOKEN;
    const certData = env.SANDBOX_K8S_CLIENT_CERT;
    const keyData = env.SANDBOX_K8S_CLIENT_KEY;
    const username = env.SANDBOX_K8S_USERNAME;
    const password = env.SANDBOX_K8S_PASSWORD;
    const contextName = env.SANDBOX_K8S_CONTEXT_NAME || 'env-ctx';
    const clusterName = env.SANDBOX_K8S_CLUSTER_NAME || 'env-cluster';
    const userName = env.SANDBOX_K8S_USER_NAME || 'env-user';
    const kc = new KubeConfig();
    kc.loadFromOptions({
      clusters: [
        {
          name: clusterName,
          server,
          caData,
          skipTLSVerify: skipTLS,
        },
      ],
      users: [
        {
          name: userName,
          token,
          certData,
          keyData,
          username,
          password,
        },
      ],
      contexts: [
        {
          name: contextName,
          cluster: clusterName,
          user: userName,
          namespace: this.opts?.namespace || 'default',
        },
      ],
      currentContext: contextName,
    });
    return kc;
  }

  async start(): Promise<string | undefined> {
    if (this.started) return this.podName;
    await this.loadClient();
    this.podName = this.opts.podName ?? `sandbox-${randomUUID().slice(0, 8)}`;
    const spec = {
      metadata: { name: this.podName, labels: { app: 'sandbox' } },
      spec: {
        serviceAccountName: this.opts.serviceAccountName || undefined,
        restartPolicy: 'Never',
        containers: [
          {
            name: 'main',
            image: this.opts.image,
            imagePullPolicy: this.opts.pullPolicy,
            command: [
              'sh',
              '-lc',
              `mkdir -p ${this.opts.workdir} && sleep ${this.opts.idleSeconds}`,
            ],
            workingDir: this.opts.workdir,
            resources: {
              limits: { memory: `${this.opts.memoryLimitMb}Mi`, cpu: this.opts.cpuLimit },
              // Requests default: 25% of limit (min 64Mi / 50m)
              requests: {
                memory: `${Math.max(64, Math.floor(this.opts.memoryLimitMb * 0.25))}Mi`,
                cpu: this.deriveCpuRequest(this.opts.cpuLimit),
              },
            },
          },
        ],
      },
    };
    try {
      if (!this.core) throw new Error('Core API not initialized');
      await this.core.createNamespacedPod({ namespace: this.opts.namespace, body: spec });
    } catch (e: unknown) {
      if ((e as { body?: { reason?: string } })?.body?.reason !== 'AlreadyExists') throw e;
    }
    // Wait until running
    const maxWait = Date.now() + 20_000;
    while (Date.now() < maxWait) {
      try {
        if (!this.core) throw new Error('Core API not initialized');
        const podObj = await this.core.readNamespacedPod({
          name: this.podName,
          namespace: this.opts.namespace,
        });
        if (podObj?.status?.phase === 'Running') {
          this.started = true;
          return this.podName;
        }
      } catch {
        /* ignore */
      }
      await new Promise((r) => setTimeout(r, 400));
    }
    throw new Error('Pod did not reach Running state');
  }

  async stop(): Promise<void> {
    if (!this.started || !this.podName) return;
    try {
      if (!this.core) return;
      await this.core.deleteNamespacedPod({ name: this.podName, namespace: this.opts.namespace });
    } catch {
      /* ignore */
    }
    this.started = false;
    this.podName = undefined;
  }

  private ensureStarted() {
    if (!this.started || !this.podName) throw new Error('Sandbox not started');
    return this.podName;
  }

  private async execCommand(
    cmd: string,
    opts: { timeoutMs?: number; input?: string } = {},
  ): Promise<SandboxExecutionResult> {
    await this.start();
    const pod = this.ensureStarted();
    if (!this.kc) throw new Error('KubeConfig not loaded');
    const exec = new Exec(this.kc);
    const stdoutStream = new PassThrough();
    const stderrStream = new PassThrough();
    const stdoutChunks: string[] = [];
    const stderrChunks: string[] = [];
    stdoutStream.on('data', (d) => stdoutChunks.push(d.toString()));
    stderrStream.on('data', (d) => stderrChunks.push(d.toString()));
    const { timeoutMs = 10_000 } = opts;
    const start = Date.now();
    let timedOut = false;
    let exitCode: number | null = null;
    const stdin = opts.input != null ? Readable.from([opts.input]) : undefined;
    const commandArr = ['sh', '-lc', cmd];
    await new Promise<void>((resolve) => {
      const timer = setTimeout(() => {
        timedOut = true;
        resolve();
      }, timeoutMs).unref();
      exec
        .exec(
          this.opts.namespace,
          pod,
          'main',
          commandArr,
          stdoutStream,
          stderrStream,
          stdin as unknown as Readable,
          false,
          (status: unknown) => {
            clearTimeout(timer);
            if (status && typeof (status as { exitCode?: number }).exitCode === 'number') {
              exitCode = (status as { exitCode: number }).exitCode;
            } else if (status && typeof (status as { code?: number }).code === 'number') {
              exitCode = (status as { code: number }).code;
            }
            resolve();
          },
        )
        .catch((err: unknown) => {
          clearTimeout(timer);
          stderrChunks.push(String(err));
          resolve();
        });
    });
    return {
      stdout: stdoutChunks.join(''),
      stderr: stderrChunks.join(''),
      exitCode,
      timedOut,
      durationMs: Date.now() - start,
    };
  }

  async writeFile(opts: SandboxFsWriteOptions): Promise<void> {
    const data =
      typeof opts.content === 'string' ? opts.content : Buffer.from(opts.content).toString('utf8');
    const b64 = Buffer.from(data).toString('base64');
    const quotedPath = this.quotePath(opts.path);
    const mode = opts.executable ? '755' : '644';
    await this.execCommand(
      `dir=$(dirname ${quotedPath}) && mkdir -p "$dir" && echo '${b64}' | base64 -d > ${quotedPath} && chmod ${mode} ${quotedPath}`,
      { timeoutMs: 15_000 },
    );
  }

  async readFile(path: string): Promise<string> {
    const res = await this.execCommand(`cat ${this.quotePath(path)}`);
    return res.stdout;
  }

  async list(path = '.'): Promise<string[]> {
    const res = await this.execCommand(`ls -1 ${this.quotePath(path)}`);
    return res.stdout.split('\n').filter(Boolean);
  }

  async remove(path: string): Promise<void> {
    await this.execCommand(`rm -rf ${this.quotePath(path)}`);
  }

  async shell(opts: SandboxShellOptions): Promise<SandboxExecutionResult> {
    const cmdStr = Array.isArray(opts.cmd) ? opts.cmd.join(' ') : opts.cmd;
    const workdir = opts.workdir ?? this.opts.workdir;
    const envAssign = opts.env
      ? Object.entries(opts.env)
          .map(([k, v]) => `${k}='${String(v).replace(/'/g, "'\\''")}'`)
          .join(' ')
      : '';
    const full = `${envAssign ? `${envAssign} ` : ''}cd ${this.quotePath(workdir)} && ${cmdStr}`;
    return this.execCommand(full, { timeoutMs: opts.timeoutMs, input: opts.input });
  }

  async runCode(options: SandboxCodeOptions): Promise<SandboxExecutionResult> {
    const filename = `snippet-${randomUUID().slice(0, 8)}.${
      options.language === 'python' ? 'py' : options.language === 'typescript' ? 'ts' : 'js'
    }`;
    await this.writeFile({ path: `${this.opts.workdir}/${filename}`, content: options.code });
    let cmd: string;
    switch (options.language) {
      case 'javascript':
        cmd = `node ${filename}`;
        break;
      case 'typescript':
        cmd = `npx ts-node ${filename}`;
        break;
      case 'python':
        cmd = `python ${filename}`;
        break;
      default:
        throw new Error('Unsupported language');
    }
    return this.shell({ cmd, timeoutMs: options.timeoutMs });
  }

  private quotePath(p: string): string {
    if (p === '.' || p === '') return p;
    return `'${p.replace(/'/g, "'\\''")}'`;
  }
}

export default KubernetesSandboxProvider;
