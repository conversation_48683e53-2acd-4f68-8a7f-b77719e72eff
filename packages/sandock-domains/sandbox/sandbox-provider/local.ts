import { exec as cbExec, spawn } from 'node:child_process';
import { randomUUID } from 'node:crypto';
import {
  readFile as fsReadFile,
  writeFile as fsWriteFile,
  mkdtemp,
  readdir,
  rm,
} from 'node:fs/promises';
import { tmpdir } from 'node:os';
import { join } from 'node:path';
import { promisify } from 'node:util';
import type {
  SandboxCodeOptions,
  SandboxExecutionResult,
  SandboxFsWriteOptions,
  SandboxProvider,
  SandboxShellOptions,
} from '../types';

const exec = promisify(cbExec);

export interface LocalSandboxOptions {
  workdir?: string; // custom base (temp dir if omitted)
  keep?: boolean; // do not delete on stop (for debugging)
  id?: string; // stable id maps to deterministic directory (requires workdir base)
}

export class LocalSandboxProvider implements SandboxProvider {
  private root?: string;
  private opts: Required<Pick<LocalSandboxOptions, 'keep'>> & { base?: string };
  private id?: string;

  constructor(options: LocalSandboxOptions = {}) {
    this.opts = { keep: options.keep ?? false, base: options.workdir };
    this.id = options.id;
  }

  // Local provider currently unbounded; approximate using host logical cores and a default memory cap env override
  get cpu(): number {
    const env = process.env.SANDBOX_LOCAL_CPU;
    if (env) {
      const n = Number(env);
      if (Number.isFinite(n) && n > 0) return n;
    }
    try {
      const cores = require('node:os').cpus()?.length || 1;
      // Use half cores as billing baseline
      return Math.max(0.25, +(cores / 2).toFixed(2));
    } catch {
      return 1;
    }
  }
  get mem(): number {
    const env = process.env.SANDBOX_LOCAL_MEM_MB;
    if (env) {
      const n = Number(env);
      if (Number.isFinite(n) && n > 0) return n * 1024 * 1024;
    }
    try {
      const total = require('node:os').totalmem();
      // Billable cap heuristics: 25% of host memory
      return Math.max(256 * 1024 * 1024, Math.floor(total * 0.25));
    } catch {
      return 512 * 1024 * 1024;
    }
  }

  async start(): Promise<string | undefined> {
    if (this.root) return this.root;
    if (this.id && this.opts.base) {
      // deterministic directory: base/id
      const dir = join(this.opts.base, this.id);
      await import('node:fs/promises').then(async (fs) => {
        await fs.mkdir(dir, { recursive: true });
      });
      this.root = dir;
    } else {
      this.root = this.opts.base ?? (await mkdtemp(join(tmpdir(), 'sandbox-')));
    }
    return this.root;
  }

  async stop(): Promise<void> {
    if (!this.root) return;
    if (!this.opts.keep && !(this.id && this.opts.base)) {
      // don't delete deterministic shared dir
      try {
        await rm(this.root, { recursive: true, force: true });
      } catch {}
    }
    this.root = undefined;
  }

  private ensureStarted() {
    if (!this.root) throw new Error('Sandbox not started');
    return this.root;
  }

  async writeFile(opts: SandboxFsWriteOptions): Promise<void> {
    const root = this.ensureStarted();
    const p = join(root, opts.path);
    await fsWriteFile(p, opts.content);
    if (opts.executable) {
      await import('node:fs').then((fs) => fs.chmodSync(p, 0o755));
    }
  }

  async readFile(path: string): Promise<string> {
    const root = this.ensureStarted();
    return fsReadFile(join(root, path), 'utf8');
  }

  async list(path = '.'): Promise<string[]> {
    const root = this.ensureStarted();
    return readdir(join(root, path));
  }

  async remove(path: string): Promise<void> {
    const root = this.ensureStarted();
    await rm(join(root, path), { recursive: true, force: true });
  }

  async shell(opts: SandboxShellOptions): Promise<SandboxExecutionResult> {
    const root = this.ensureStarted();
    const start = Date.now();
    const cmdStr = Array.isArray(opts.cmd) ? opts.cmd.join(' ') : opts.cmd;
    try {
      const { stdout, stderr } = await exec(cmdStr, {
        cwd: opts.workdir ? join(root, opts.workdir) : root,
        env: { ...process.env, ...opts.env },
        timeout: opts.timeoutMs ?? 10_000,
      });
      return { stdout, stderr, exitCode: 0, timedOut: false, durationMs: Date.now() - start };
    } catch (e: unknown) {
      const err = e as {
        stdout?: string;
        stderr?: string;
        code?: number;
        signal?: string;
        killed?: boolean;
        message?: string;
      };
      const timedOut: boolean = !!(
        /timed out/i.test(err.message ?? '') ||
        (err.killed && err.signal === 'SIGTERM')
      );
      return {
        stdout: err.stdout ?? '',
        stderr: err.stderr ?? err.message ?? String(e),
        exitCode: typeof err.code === 'number' ? err.code : null,
        timedOut,
        durationMs: Date.now() - start,
      };
    }
  }

  async runCode(options: SandboxCodeOptions): Promise<SandboxExecutionResult> {
    await this.start();
    const filename = this.generateFilename(options.language);
    await this.writeFile({ path: filename, content: options.code });
    let cmd: string;
    switch (options.language) {
      case 'javascript':
        cmd = `node ${filename}`;
        break;
      case 'typescript':
        cmd = `npx ts-node ${filename}`;
        break;
      case 'python':
        cmd = `python ${filename}`;
        break;
      default:
        throw new Error('Unsupported language');
    }
    return this.shell({ cmd, timeoutMs: options.timeoutMs, input: options.input });
  }

  private generateFilename(lang: SandboxCodeOptions['language']) {
    const id = randomUUID().slice(0, 8);
    return `snippet-${id}.${lang === 'python' ? 'py' : lang === 'typescript' ? 'ts' : 'js'}`;
  }

  async *shellStream(
    opts: SandboxShellOptions,
  ): AsyncIterable<
    | { type: 'stdout'; data: string }
    | { type: 'stderr'; data: string }
    | { type: 'exit'; exitCode: number | null; timedOut: boolean; durationMs: number }
  > {
    const root = this.ensureStarted();
    const start = Date.now();
    const cmdStr = Array.isArray(opts.cmd) ? opts.cmd[0] : 'sh';
    const args = Array.isArray(opts.cmd) ? opts.cmd.slice(1) : ['-lc', String(opts.cmd)];
    const child = spawn(cmdStr, args, {
      cwd: opts.workdir ? join(root, opts.workdir) : root,
      env: { ...process.env, ...opts.env },
    });
    if (opts.input) child.stdin?.end(opts.input);
    const timeoutMs = opts.timeoutMs ?? 10_000;
    const timeout: ReturnType<typeof setTimeout> | undefined = setTimeout(() => {
      child.kill('SIGKILL');
    }, timeoutMs);
    // Collect into queues and periodically flush
    const stdoutQueue: string[] = [];
    const stderrQueue: string[] = [];
    child.stdout?.on('data', (d) => stdoutQueue.push(d.toString()));
    child.stderr?.on('data', (d) => stderrQueue.push(d.toString()));
    while (true) {
      while (stdoutQueue.length) {
        const d = stdoutQueue.shift();
        if (d != null) yield { type: 'stdout', data: d } as const;
      }
      while (stderrQueue.length) {
        const d = stderrQueue.shift();
        if (d != null) yield { type: 'stderr', data: d } as const;
      }
      const exited = child.exitCode !== null || child.killed;
      if (exited) break;
      await new Promise((r) => setTimeout(r, 30));
    }
    if (timeout) clearTimeout(timeout);
    yield {
      type: 'exit',
      exitCode: child.exitCode,
      timedOut: false,
      durationMs: Date.now() - start,
    } as const;
  }

  async *runCodeStream(options: SandboxCodeOptions) {
    // fallback: run non-stream via runCode then emit aggregated
    const result = await this.runCode(options);
    if (result.stdout) yield { type: 'stdout', data: result.stdout } as const;
    if (result.stderr) yield { type: 'stderr', data: result.stderr } as const;
    yield {
      type: 'exit',
      exitCode: result.exitCode,
      timedOut: result.timedOut,
      durationMs: result.durationMs,
    } as const;
  }
}

export default LocalSandboxProvider;
