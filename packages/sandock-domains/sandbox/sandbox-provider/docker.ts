// Docker based sandbox for AI code execution (code interpreter, fs, shell)
// Responsibilities:
// 1. Start/stop ephemeral Docker containers as isolated sandboxes
// 2. Provide limited filesystem API (read/write/list/remove) within container workdir
// 3. Execute shell commands & capture stdout/stderr/exitCode
// 4. Execute code snippets (currently: node / python) by writing temp file & running interpreter
// 5. Resource limits (CPU, Memory) & auto-timeout
// 6. Basic lifecycle management & cleanup
//
// NOTE: This is a first iteration; extend with more languages, caching, mounting, stream logs, etc.

import { randomUUID } from 'node:crypto';
import { PassThrough } from 'node:stream';
import type { Container, ContainerCreateOptions, Exec as DockerExec } from 'dockerode';
import Docker from 'dockerode';
import type {
  SandboxCodeOptions,
  SandboxExecutionResult,
  SandboxFsWriteOptions,
  SandboxProvider,
  SandboxShellOptions,
} from '../types';

export interface DockerSandboxOptions {
  /** Stable identifier (container name). Enables cross-process get/restart instead of always creating new */
  id?: string;
  image?: string; // default: 'node:20-bullseye'
  pythonImage?: string; // image used when python code requested if different
  pull?: boolean; // attempt pull if image missing
  workdir?: string; // inside container
  memoryLimitMb?: number; // container memory limit
  cpuShares?: number; // relative CPU weight
  autoRemove?: boolean; // remove container on stop
}

interface ActiveContainer {
  id: string;
  container: Container;
  image: string;
  createdAt: number;
}

export class DockerSandboxProvider implements SandboxProvider {
  private docker: Docker;
  private opts: Required<
    Pick<
      DockerSandboxOptions,
      'image' | 'workdir' | 'pull' | 'pythonImage' | 'memoryLimitMb' | 'cpuShares' | 'autoRemove'
    >
  >;
  private id?: string;
  private active?: ActiveContainer;

  constructor(options: DockerSandboxOptions = {}) {
    this.docker = new Docker();
    const env = process.env;
    const parseIntPositive = (v: string | undefined, d: number) => {
      if (!v) return d;
      const n = parseInt(v, 10);
      return Number.isFinite(n) && n > 0 ? n : d;
    };
    // Allow specifying vCPU fractional, convert to cpuShares (relative weight out of 1024)
    let cpuSharesFromEnv: number | undefined;
    if (env.SANDBOX_DOCKER_CPU) {
      const vcpu = Number(env.SANDBOX_DOCKER_CPU);
      if (Number.isFinite(vcpu) && vcpu > 0)
        cpuSharesFromEnv = Math.max(2, Math.round(vcpu * 1024));
    }
    if (!cpuSharesFromEnv && env.SANDBOX_DOCKER_CPU_SHARES) {
      const shares = parseInt(env.SANDBOX_DOCKER_CPU_SHARES, 10);
      if (Number.isFinite(shares) && shares > 0) cpuSharesFromEnv = shares;
    }
    this.opts = {
      image: options.image ?? env.SANDBOX_DOCKER_IMAGE ?? 'node:20-bullseye',
      pythonImage: options.pythonImage ?? env.SANDBOX_DOCKER_PY_IMAGE ?? 'python:3.12-slim',
      pull: options.pull ?? env.SANDBOX_DOCKER_PULL === 'true',
      workdir: options.workdir ?? env.SANDBOX_DOCKER_WORKDIR ?? '/sandbox',
      memoryLimitMb: options.memoryLimitMb ?? parseIntPositive(env.SANDBOX_DOCKER_MEMORY_MB, 512),
      cpuShares: options.cpuShares ?? cpuSharesFromEnv ?? 256,
      autoRemove:
        options.autoRemove !== undefined
          ? options.autoRemove
          : env.SANDBOX_DOCKER_AUTO_REMOVE
            ? env.SANDBOX_DOCKER_AUTO_REMOVE === 'true'
            : !options.id,
    };
    this.id = options.id;
  }

  // Approximate CPU (Docker cpu shares relative to 1024 -> convert to cores fraction)
  get cpu(): number {
    // cpuShares default 256 -> 0.25 cores relative to base 1024
    return +(this.opts.cpuShares / 1024).toFixed(3);
  }
  // Memory bytes
  get mem(): number {
    return this.opts.memoryLimitMb * 1024 * 1024;
  }

  /** Ensure the base image exists (pull optionally) */
  private async ensureImage(image: string) {
    try {
      await this.docker.getImage(image).inspect();
    } catch {
      if (!this.opts.pull) throw new Error(`Image ${image} not found locally and pull disabled`);
      await new Promise<void>((resolve, reject) => {
        this.docker.pull(
          image,
          (error: Error | undefined, stream: NodeJS.ReadableStream | undefined) => {
            if (error) return reject(error);
            if (!stream) return reject(new Error('No pull stream'));
            const modem = this.docker.modem as unknown as {
              followProgress?: (s: NodeJS.ReadableStream, cb: (err: Error | null) => void) => void;
            };
            if (modem.followProgress)
              modem.followProgress(stream, (err2) => (err2 ? reject(err2) : resolve()));
            else resolve();
          },
        );
      });
    }
  }

  /** Start a new container for this sandbox */
  async start(imageOverride?: string) {
    if (this.active) return this.active.id;
    const image = imageOverride ?? this.opts.image;
    // If we have a stable id, attempt to locate existing container first
    if (this.id) {
      const existing = await this.findExistingContainer(this.id);
      if (existing) {
        // If image mismatch, recreate (to avoid stale image)
        if (existing.image !== image) {
          try {
            await existing.container.remove({ force: true });
          } catch {}
        } else {
          // restart existing container to ensure fresh process state
          try {
            await existing.container.restart();
          } catch {
            // If restart fails (e.g., was exited), try start
            try {
              await existing.container.start();
            } catch {}
          }
          this.active = existing;
          await this.execInternal(['mkdir', '-p', this.opts.workdir]);
          return existing.id;
        }
      }
    }

    await this.ensureImage(image);
    const createOptions: ContainerCreateOptions = {
      Image: image,
      Tty: false,
      WorkingDir: this.opts.workdir,
      HostConfig: {
        AutoRemove: this.opts.autoRemove,
        Memory: this.opts.memoryLimitMb * 1024 * 1024,
        CpuShares: this.opts.cpuShares,
        NetworkMode: 'none', // isolation
        PidsLimit: 256,
        ReadonlyRootfs: false,
      },
      Env: ['NODE_NO_WARNINGS=1', 'PYTHONDONTWRITEBYTECODE=1'],
      Cmd: ['sleep', '3600'], // long running idle process
      name: this.id, // only applied if defined
    } as ContainerCreateOptions & { name?: string };
    const container = await this.docker.createContainer(createOptions);
    await container.start();
    this.active = { id: container.id, container, image, createdAt: Date.now() };
    await this.execInternal(['mkdir', '-p', this.opts.workdir]);
    return container.id;
  }

  async stop() {
    if (!this.active) return;
    try {
      await this.active.container.kill({ signal: 'SIGKILL' });
    } catch (_) {
      /* ignore */
    }
    try {
      if (!this.opts.autoRemove) await this.active.container.remove({ force: true });
    } catch (_) {
      /* ignore */
    }
    this.active = undefined;
  }

  private ensureStarted() {
    if (!this.active) throw new Error('Sandbox not started');
    return this.active.container;
  }

  /** Locate existing container by name (id) */
  private async findExistingContainer(id: string): Promise<ActiveContainer | undefined> {
    try {
      const list = await this.docker.listContainers({ all: true, filters: { name: [id] } });
      const info = list.find((c) => (c.Names || []).includes(`/${id}`));
      if (!info) return undefined;
      const container = this.docker.getContainer(info.Id);
      // Need image name (RepoTags[0]) requires inspect
      const inspect = await container.inspect();
      return {
        id: info.Id,
        container,
        image: inspect.Config?.Image || info.Image,
        createdAt: Date.parse(inspect.Created || new Date().toISOString()),
      };
    } catch {
      return undefined;
    }
  }

  // Basic filesystem write inside container using tar archive (dockerode putArchive)
  async writeFile(opts: SandboxFsWriteOptions) {
    const container = this.ensureStarted();
    const { path, content, executable } = opts;
    const tar = await import('tar-stream');
    const pack = tar.pack();
    const mode = executable ? 0o755 : 0o644;
    const dataBuffer = typeof content === 'string' ? Buffer.from(content) : Buffer.from(content);
    pack.entry({ name: path.replace(/^\/+/, ''), mode, size: dataBuffer.length }, dataBuffer);
    pack.finalize();
    await (
      container as unknown as { putArchive: (a: unknown, o: unknown) => Promise<void> }
    ).putArchive(pack, { path: this.opts.workdir });
  }

  async readFile(path: string): Promise<string> {
    const c = this.ensureStarted();
    const exec = await c.exec({
      Cmd: ['cat', path],
      AttachStdout: true,
      AttachStderr: true,
      WorkingDir: this.opts.workdir,
    });
    const { stdout } = await this.collectExec(exec);
    return stdout;
  }

  async list(path = '.') {
    const c = this.ensureStarted();
    const exec = await c.exec({
      Cmd: ['sh', '-lc', `ls -1 ${path}`],
      AttachStdout: true,
      AttachStderr: true,
      WorkingDir: this.opts.workdir,
    });
    const { stdout } = await this.collectExec(exec);
    return stdout.split('\n').filter(Boolean);
  }

  async remove(path: string) {
    this.ensureStarted();
    await this.execInternal(['rm', '-rf', path]);
  }

  async shell(opts: SandboxShellOptions): Promise<SandboxExecutionResult> {
    const container = this.ensureStarted();
    const cmdArray = Array.isArray(opts.cmd) ? opts.cmd : ['sh', '-lc', opts.cmd];
    const exec = await container.exec({
      Cmd: cmdArray,
      AttachStdout: true,
      AttachStderr: true,
      AttachStdin: !!opts.input,
      WorkingDir: opts.workdir ?? this.opts.workdir,
      Env: opts.env ? Object.entries(opts.env).map(([k, v]) => `${k}=${v}`) : undefined,
    });
    return this.collectExec(exec, opts.timeoutMs ?? 10_000, opts.input);
  }

  async runCode(options: SandboxCodeOptions): Promise<SandboxExecutionResult> {
    // select appropriate image if language requires python & current container image not python
    if (options.language === 'python' && (!this.active || !this.active.image.includes('python'))) {
      if (this.active) await this.stop();
      await this.start(this.opts.pythonImage);
    } else if (!this.active) {
      await this.start();
    }
    const filename = this.generateFilename(options.language);
    const codeToWrite = this.prepareCode(options);
    await this.writeFile({ path: filename, content: codeToWrite });
    let cmd: string;
    switch (options.language) {
      case 'typescript':
        // use ts-node/register if available else attempt npx ts-node
        cmd = `bash -lc 'if command -v node >/dev/null && [ -f package.json ]; then (npm ls ts-node >/dev/null 2>&1 || npm i -y ts-node typescript >/dev/null 2>&1); node -e "require('ts-node/register'); require('./${filename}')"; else echo "No node"; fi'`;
        break;
      case 'javascript':
        cmd = `node ${filename}`;
        break;
      case 'python':
        cmd = `python ${filename}`;
        break;
      default:
        throw new Error('Unsupported language');
    }
    return this.shell({ cmd, timeoutMs: options.timeoutMs ?? 15_000, input: options.input });
  }

  private generateFilename(lang: SandboxCodeOptions['language']) {
    const id = randomUUID().slice(0, 8);
    return `snippet-${id}.${lang === 'python' ? 'py' : lang === 'typescript' ? 'ts' : 'js'}`;
  }

  private prepareCode(options: SandboxCodeOptions) {
    if (options.language === 'typescript') {
      return options.code; // rely on ts-node/register path
    }
    return options.code;
  }

  private async execInternal(cmd: string[]) {
    const container = this.ensureStarted();
    const exec = await container.exec({
      Cmd: cmd,
      AttachStdout: true,
      AttachStderr: true,
      WorkingDir: this.opts.workdir,
    });
    return this.collectExec(exec);
  }

  private collectExec(
    exec: DockerExec,
    timeoutMs = 10_000,
    input?: string,
  ): Promise<SandboxExecutionResult> {
    return new Promise((resolve, reject) => {
      const start = Date.now();
      let stdout = '';
      let stderr = '';
      let finished = false;
      let timeout: ReturnType<typeof setTimeout> | undefined;
      const handleDone = (exitCode: number | null, timedOut = false) => {
        if (finished) return;
        finished = true;
        if (timeout) clearTimeout(timeout);
        resolve({ stdout, stderr, exitCode, timedOut, durationMs: Date.now() - start });
      };
      exec.start(
        { hijack: true, stdin: !!input },
        (err: Error | undefined, stream: NodeJS.ReadableStream | undefined) => {
          if (err) return reject(err);
          if (!stream) return reject(new Error('No exec stream'));
          const stdoutStream = new PassThrough();
          const stderrStream = new PassThrough();
          // dockerode does not export a typed modem, restrict to unknown
          const modem: unknown = (exec as unknown as { modem?: unknown }).modem;
          if (modem && typeof (modem as { demuxStream?: unknown }).demuxStream === 'function') {
            (
              modem as {
                demuxStream: (s: NodeJS.ReadableStream, o: PassThrough, e: PassThrough) => void;
              }
            ).demuxStream(stream, stdoutStream, stderrStream);
          } else {
            // Fallback: just pipe all to stdout
            stream.pipe(stdoutStream);
          }
          stdoutStream.on('data', (d) => {
            stdout += d.toString();
          });
          stderrStream.on('data', (d) => {
            stderr += d.toString();
          });
          stream.on('end', async () => {
            try {
              const inspect = await exec.inspect();
              handleDone(inspect.ExitCode);
            } catch {
              handleDone(null);
            }
          });
          if (input) {
            // write and close stdin
            if (
              'write' in stream &&
              typeof (stream as { write: (d: string) => void }).write === 'function'
            ) {
              (stream as { write: (d: string) => void }).write(input);
            }
            if ('end' in stream && typeof (stream as { end: () => void }).end === 'function') {
              (stream as { end: () => void }).end();
            }
          }
        },
      );
      timeout = setTimeout(async () => {
        try {
          const container = this.active?.container;
          if (container) await container.kill({ signal: 'SIGKILL' });
        } catch (_) {}
        handleDone(null, true);
      }, timeoutMs);
    });
  }

  async *shellStream(
    opts: SandboxShellOptions,
  ): AsyncIterable<
    | { type: 'stdout'; data: string }
    | { type: 'stderr'; data: string }
    | { type: 'exit'; exitCode: number | null; timedOut: boolean; durationMs: number }
  > {
    const container = this.ensureStarted();
    const cmdArray = Array.isArray(opts.cmd) ? opts.cmd : ['sh', '-lc', opts.cmd];
    const exec = await container.exec({
      Cmd: cmdArray,
      AttachStdout: true,
      AttachStderr: true,
      AttachStdin: !!opts.input,
      WorkingDir: opts.workdir ?? this.opts.workdir,
      Env: opts.env ? Object.entries(opts.env).map(([k, v]) => `${k}=${v}`) : undefined,
    });
    const start = Date.now();
    const timeoutMs = opts.timeoutMs ?? 10_000;
    let timedOut = false;
    const stdoutBuf: string[] = [];
    const stderrBuf: string[] = [];
    await new Promise<void>((resolve, reject) => {
      exec.start({ hijack: true, stdin: !!opts.input }, (err, stream) => {
        if (err) return reject(err);
        if (!stream) return reject(new Error('No exec stream'));
        const stdoutStream = new PassThrough();
        const stderrStream = new PassThrough();
        const modem: unknown = (exec as unknown as { modem?: unknown }).modem;
        if (modem && typeof (modem as { demuxStream?: unknown }).demuxStream === 'function') {
          (
            modem as {
              demuxStream: (s: NodeJS.ReadableStream, o: PassThrough, e: PassThrough) => void;
            }
          ).demuxStream(stream, stdoutStream, stderrStream);
        } else stream.pipe(stdoutStream);
        stdoutStream.on('data', (d) => stdoutBuf.push(d.toString()));
        stderrStream.on('data', (d) => stderrBuf.push(d.toString()));
        stream.on('end', () => resolve());
        if (opts.input) {
          // dynamic optional chaining writes
          // @ts-ignore
          stream.write?.(opts.input);
          // @ts-ignore
          stream.end?.();
        }
      });
      setTimeout(async () => {
        try {
          const c = this.active?.container;
          if (c) await c.kill({ signal: 'SIGKILL' });
          timedOut = true;
        } catch {}
      }, timeoutMs).unref();
    });
    while (stdoutBuf.length) {
      const d = stdoutBuf.shift();
      if (d != null) yield { type: 'stdout', data: d } as const;
    }
    while (stderrBuf.length) {
      const d = stderrBuf.shift();
      if (d != null) yield { type: 'stderr', data: d } as const;
    }
    let exitCode: number | null = null;
    try {
      const inspect = await exec.inspect();
      exitCode = inspect.ExitCode;
    } catch {}
    yield {
      type: 'exit',
      exitCode,
      timedOut,
      durationMs: Date.now() - start,
    } as const;
  }

  async *runCodeStream(options: SandboxCodeOptions) {
    // prepare like runCode then stream shell
    if (options.language === 'python' && (!this.active || !this.active.image.includes('python'))) {
      if (this.active) await this.stop();
      await this.start(this.opts.pythonImage);
    } else if (!this.active) {
      await this.start();
    }
    const filename = this.generateFilename(options.language);
    const codeToWrite = this.prepareCode(options);
    await this.writeFile({ path: filename, content: codeToWrite });
    let cmd: string;
    switch (options.language) {
      case 'typescript':
        cmd = `bash -lc 'if command -v node >/dev/null && [ -f package.json ]; then (npm ls ts-node >/dev/null 2>&1 || npm i -y ts-node typescript >/dev/null 2>&1); node -e "require('ts-node/register'); require('./${filename}')"; else echo "No node"; fi'`;
        break;
      case 'javascript':
        cmd = `node ${filename}`;
        break;
      case 'python':
        cmd = `python ${filename}`;
        break;
      default:
        throw new Error('Unsupported language');
    }
    for await (const ev of this.shellStream({
      cmd,
      timeoutMs: options.timeoutMs,
      input: options.input,
    })) {
      yield ev;
    }
  }
}

export default DockerSandboxProvider;
