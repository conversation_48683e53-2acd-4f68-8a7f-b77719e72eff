// Types and interfaces for sandbox providers.
// Shared sandbox type definitions & provider interface
// Designed to allow multiple backend implementations (local, docker, kubernetes)

export interface SandboxExecutionResult {
  stdout: string;
  stderr: string;
  exitCode: number | null;
  timedOut: boolean;
  durationMs: number;
}

export interface SandboxCodeOptions {
  language: 'javascript' | 'typescript' | 'python';
  code: string;
  timeoutMs?: number; // default 15s
  input?: string; // stdin
}

export interface SandboxShellOptions {
  cmd: string | string[];
  timeoutMs?: number; // default 10s
  workdir?: string;
  env?: Record<string, string>;
  input?: string;
}

export interface SandboxFsWriteOptions {
  path: string; // relative inside sandbox root
  content: string | Uint8Array;
  executable?: boolean;
}

export interface BaseSandboxOptions {
  workdir?: string; // internal working directory root
}

export interface SandboxProvider {
  /** Start sandbox resources (container, pod, temp dir). Returns sandbox id or workdir path. */
  start(): Promise<string | undefined>;
  /** Stop & cleanup */
  stop(): Promise<void>;
  /** Write file relative to sandbox root */
  writeFile(opts: SandboxFsWriteOptions): Promise<void>;
  readFile(path: string): Promise<string>;
  list(path?: string): Promise<string[]>;
  remove(path: string): Promise<void>;
  shell(opts: SandboxShellOptions): Promise<SandboxExecutionResult>;
  runCode(options: SandboxCodeOptions): Promise<SandboxExecutionResult>;
  // Optional streaming versions
  // shellStream?(
  //   opts: SandboxShellOptions,
  // ): AsyncIterable<
  //   | { type: 'stdout'; data: string }
  //   | { type: 'stderr'; data: string }
  //   | { type: 'exit'; exitCode: number | null; timedOut: boolean; durationMs: number }
  // >;
  // runCodeStream?(
  //   opts: SandboxCodeOptions,
  // ): AsyncIterable<
  //   | { type: 'stdout'; data: string }
  //   | { type: 'stderr'; data: string }
  //   | { type: 'exit'; exitCode: number | null; timedOut: boolean; durationMs: number }
  // >;
  /** vCPU cores (fractional allowed, eg 0.5) allocated / enforced for this sandbox. */
  readonly cpu: number;
  /** Memory bytes limit (approx). Use mem / 1024^3 for GiB billing. 0 if unbounded. */
  readonly mem: number;
}

// NOTE: Removed SandboxProviderName alias (was = DbSandboxProvider) to avoid redundancy.
