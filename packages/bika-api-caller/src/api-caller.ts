import { errors } from '@bika/contents/config/server/error/errors';
import { Mutation<PERSON>ache, QueryCache, QueryClient } from '@tanstack/react-query';
import { httpBatchLink, TRPCClientError, type TRPCLink } from '@trpc/client';
import { observable } from '@trpc/server/observable';
import axios, { type AxiosInstance } from 'axios';
import { EventSource } from 'eventsource';
import type toastFunc from 'react-hot-toast';
import { z } from 'zod';
import { fromError } from 'zod-validation-error';
import { API_ROOT_URL, type AppRouter, TRPC_ROOT_URL, type TRPCOriginClient, trpc } from './consts';
import { useApiUsageLimitErrorStore } from './error-store';

/**
 * Helper function to format ZodError using zod-validation-error for better user experience
 * @param error TRPCClientError that might contain ZodError data
 * @returns Formatted error message or null if no ZodError found
 */
function formatZodErrorMessage(error: TRPCClientError<any>): string | null {
  // First, check if error.data.message contains a JSON string with ZodError issues
  if (error?.data?.message && typeof error.data.message === 'string') {
    try {
      const parsedMessage = JSON.parse(error.data.message);
      // Check if it's an array of ZodError issues
      if (Array.isArray(parsedMessage) && parsedMessage.length > 0) {
        const firstIssue = parsedMessage[0];
        if (
          firstIssue &&
          typeof firstIssue === 'object' &&
          'code' in firstIssue &&
          'message' in firstIssue
        ) {
          // This looks like ZodError issues, reconstruct ZodError and format it using zod-validation-error
          const zodError = new z.ZodError(parsedMessage);
          const validationError = fromError(zodError);
          return validationError.toString();
        }
      }
    } catch (e) {
      // Not a valid JSON or not ZodError format, continue to other checks
    }
  }

  return null;
}

export class APICaller {
  private _basePath: string;

  private _trpcURL: string;

  private _toast?: typeof toastFunc;

  // App API
  private _apiURL: string;

  private _trpcClient?: TRPCOriginClient;

  private _headers: Record<string, string> | undefined;

  private _reactQueryClient?: QueryClient;

  public get trpcClient(): TRPCOriginClient {
    if (!this._trpcClient) {
      const url = this._trpcURL;
      const headers = this._headers;
      // const toast = this._toast;
      const errorHandlingLink: TRPCLink<AppRouter> =
        () =>
        ({ next, op }) =>
          observable((observer) => {
            const unsubscribe = next(op).subscribe({
              next(value) {
                observer.next(value);
              },
              error(error) {
                // if (error.data) {
                //   const errorMessage = error.data.message;
                //   // 可根据特定的错误码，不做默认toast提示, 在外面组件里自己提示
                //   toast?.error(errorMessage);
                //   if (error.data.code === errors.billing.usage_exceed_limit.code) {
                //     // 使用限制错误，设置到store中
                //     useApiUsageLimitErrorStore.getState().setError(errorMessage);
                //   }
                // } else {
                //   toast?.error(error.message);
                // }
                observer.error(error);
              },
              complete() {
                observer.complete();
              },
            });
            return unsubscribe;
          });
      this._trpcClient = trpc.createClient({
        links: [errorHandlingLink, httpBatchLink({ url, headers })],
      });
    }
    return this._trpcClient!;
  }

  /**
   * Get the axios client directly
   */
  public get axios() {
    return axios;
  }

  /**
   * 创建 新的Sse连接，客户端
   *
   * @returns
   */
  public newSse() {
    const onlineSessionId = APICaller.genId();
    return {
      eventSource: new EventSource(`${this._basePath}/api/sse/${onlineSessionId}`, {
        // headers: this._headers,
        fetch: (input, init) =>
          fetch(input, {
            ...init,
            headers: {
              ...(init?.headers || {}),
              ...this._headers,
            },
          }),
      }),
      onlineSessionId,
    };
  }

  public get subscriber() {
    const onlineSessionId = APICaller.genId();
    return new EventSource(`${this._basePath}/api/sse/subsribe/${onlineSessionId}`, {
      // headers: this._headers,
      fetch: (input, init) =>
        fetch(input, {
          ...init,
          headers: {
            ...(init?.headers || {}),
            ...this._headers,
          },
        }),
    });
  }

  private _axiosClient?: AxiosInstance;

  /**
   * 应用程序API的axios客户端
   */
  public get apiClient() {
    if (this._axiosClient) return this._axiosClient!;

    this._axiosClient = axios.create({
      baseURL: this._apiURL, // Set the base URL for the requests
      timeout: 5000, // Set a timeout (in milliseconds) for the requests
      headers: this._headers,
    });
    return this._axiosClient!;
  }

  /**
   * 获取边缘服务配置，中心中控配置
   */
  public async getEdgeConfig(): Promise<{
    // 主网站地址
    url: string;
    // 中国版地址
    cnUrl: string;

    // 当前线上版本，如果运行环境的版本，大于线上版本，可理解为审核环境
    version: string;
  }> {
    const url = 'https://edge.bika.ai/api/config';
    const res = await this.axios.get(url);
    return res.data;
  }

  public get reactQueryClient() {
    if (!this._reactQueryClient)
      this._reactQueryClient = new QueryClient({
        defaultOptions: {
          queries: {
            retry: 3,
            refetchOnWindowFocus: false,
          },
        },
        // 下面是防止覆盖defaultOptions配置导致全局错误处理失效, 效果跟上面一样
        queryCache: new QueryCache({
          onError: (error, query) => {
            // catch query error in one place
            // console.error('global query error:', error);
            const skipError = query.meta?.skipError;
            if (skipError) return;
            if (error instanceof TRPCClientError) {
              console.log('TRPCClientError detected:', error);
              console.log('error.data:', error.data);
              if (error?.data?.code) {
                // Try to format ZodError first for better user experience
                const zodErrorMessage = formatZodErrorMessage(error);
                const errorMessage = zodErrorMessage || String(error?.data?.message);

                if (error?.data?.code === errors.billing.usage_exceed_limit.code) {
                  // 使用限制错误，设置到store中
                  useApiUsageLimitErrorStore.getState().setError(errorMessage);
                } else {
                  // 可根据特定的错误码，不做默认toast提示, 在外面组件里自己提示
                  this._toast?.error(errorMessage);
                }
              } else {
                // No error.data.code, but still try to format ZodError from message
                const zodErrorMessage = formatZodErrorMessage(error);
                const errorMessage = zodErrorMessage || error.message;
                this._toast?.error(errorMessage);
              }
              // if (error?.data.httpStatus === 401) {
              //   // 401 未授权，跳转到登录页
              //   window.location.href = '/login';
              // }
            } else {
              this._toast?.error(error.message);
            }
          },
        }),
        mutationCache: new MutationCache({
          onError: (error, _variables, _context, mutation) => {
            // catch mutation error in one place
            // console.error('global mutate error:', error);
            const skipError = mutation.options.meta?.skipError;
            if (skipError) return;
            if (error instanceof TRPCClientError) {
              if (error?.data?.code) {
                // Try to format ZodError first for better user experience
                const zodErrorMessage = formatZodErrorMessage(error);
                const errorMessage = zodErrorMessage || String(error?.data?.message);

                if (error?.data?.code === errors.billing.usage_exceed_limit.code) {
                  // 使用限制错误，设置到store中
                  useApiUsageLimitErrorStore.getState().setError(errorMessage);
                } else {
                  // 可根据特定的错误码，不做默认toast提示, 在外面组件里自己提示
                  this._toast?.error(errorMessage);
                }
              }
            } else {
              this._toast?.error(error.message);
            }
          },
        }),
      });

    return this._reactQueryClient;
  }

  /**
   * API服务器的完整地址传入
   *
   * basePath默认为空，即用同个域名
   * 你可以传入不同的域名
   * @param bikaBasePath 通常不带/api，只有域名，如https://bika.ai, http://localhost:3000
   * @param headers
   */
  constructor({
    bikaBasePath,
    headers,
    toast,
  }: {
    bikaBasePath?: string;
    headers?: Record<string, string>;
    toast?: typeof toastFunc;
  }) {
    this._basePath = bikaBasePath || '';
    this._trpcURL = this._basePath + TRPC_ROOT_URL;
    this._apiURL = this._basePath + API_ROOT_URL;
    this._headers = headers;
    this._toast = toast;
  }

  /**
   * 获取服务器延迟的ms毫秒
   *
   * @returns
   */
  async pingLattency() {
    const startTime = new Date().getTime();
    await this.apiClient.get('/meta'); // /api/meta
    const endTime = new Date().getTime();
    const latency = endTime - startTime;
    return latency;
  }

  static genId() {
    return `ose${new Date().getTime()}${APICaller.generateRandomString(10)}`;
  }

  static generateRandomString(length: number) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomString = '';

    for (let i = 0; i < length; i += 1) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      randomString += characters.charAt(randomIndex);
    }

    return randomString;
  }
}
