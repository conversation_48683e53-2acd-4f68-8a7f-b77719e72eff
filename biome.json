{"$schema": "https://biomejs.dev/schemas/2.2.2/schema.json", "assist": {"actions": {"source": {"organizeImports": "on"}}, "enabled": true}, "files": {"includes": ["**", "!**/scripts/cookbook-pdf/book", "!**/.data", "!**/.next", "!**/out", "!**/plugin_packages", "!**/node_modules", "!**/dist", "!**/build", "!**/coverage", "!**/pnpm-lock.yaml", "!**/storybook-static", "!**/prisma-client", "!**/public", "!**/projects/bika/packages/bika-zapier/lib"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "javascript": {"formatter": {"quoteStyle": "single"}, "globals": ["React", "JSX"]}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"noStaticElementInteractions": "warn", "useKeyWithClickEvents": "warn", "useKeyWithMouseEvents": "warn"}, "complexity": {"noStaticOnlyClass": "off"}, "correctness": {"noNestedComponentDefinitions": "warn", "noUnknownFunction": "warn", "useExhaustiveDependencies": "warn", "useHookAtTopLevel": "warn", "useUniqueElementIds": "warn"}, "performance": {"noImgElement": "warn"}, "security": {"noDangerouslySetInnerHtml": "off"}, "style": {}, "suspicious": {"noArrayIndexKey": "off", "noTsIgnore": "off", "noExplicitAny": "warn", "noThenProperty": "warn", "noUnknownAtRules": "warn", "useDefaultSwitchClauseLast": "warn"}}}, "overrides": []}