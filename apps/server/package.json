{"name": "@bika/server", "description": "Standalone server for Node.js, Edge Computing, tRPC and REST API included only.", "version": "2.0.1-alpha.14", "author": "", "exports": {"./servers/*": "./src/servers/*.ts", "./apis": "./src/apis/index.ts", "./client-types": "./src/client-types.ts"}, "dependencies": {"@bika/api-caller": "workspace:*", "@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/server-orm": "workspace:*", "@bika/types": "workspace:*", "@hocuspocus/extension-database": "^2.13.6", "@hocuspocus/extension-logger": "^2.13.6", "@hocuspocus/extension-redis": "^2.13.6", "@hocuspocus/server": "^2.13.6", "@hono/node-server": "^1.8.1", "@hono/node-ws": "^1.0.4", "@hono/trpc-server": "^0.4.0", "@trpc/server": "^11.4.4", "apitable": "^1.3.0", "basenext": "workspace:*", "hono": "^4.8.4", "sharelib": "workspace:*", "zod": "^3.25.0"}, "devDependencies": {"@types/node": "^20.11.24", "esbuild": "0.20.2", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "keywords": [], "license": "ISC", "main": "src/servers/node-server.ts", "private": true, "scripts": {"test": "dotenv -e ../../apps/web/.env.local -- vitest --run --reporter=verbose", "test:watch": "dotenv -e ../../apps/web/.env.local -- vitest --reporter=verbose", "test:coverage": "dotenv -e ../../apps/web/.env.local -- vitest --run --coverage --reporter=verbose", "test:concurrent": "dotenv -e ../../apps/web/.env.local -- vitest --run --reporter=verbose --pool=threads --poolOptions.threads.maxThreads=4", "test:single": "dotenv -e ../../apps/web/.env.local -- vitest --run --reporter=verbose --pool=forks --poolOptions.forks.singleFork=true", "build": "npm run build:node && npm run build:doc", "build:node": "esbuild src/servers/node-server.ts --sourcemap --minify --bundle --platform=node --external:prisma --external:prettier --external:@node-rs/argon2 --external:@node-rs/bcrypt --external:@bika.ai/license --outfile=dist/server.js", "build:doc": "esbuild src/servers/doc-server.ts --sourcemap --minify --bundle --platform=node --external:prisma --external:prettier --external:@node-rs/argon2 --external:@node-rs/bcrypt --external:@bika.ai/license --outfile=dist/server-doc.js", "build:bun:exe": "bun build ./src/servers/bun-server.ts --compile --outfile=bika-server", "build:bun": "bun build ./src/servers/bun-server.ts --outdir build --target=bun", "dev": "npm run dev:tsx", "dev:doc": "dotenv -e ../../apps/web/.env.local -- tsx watch src/servers/doc-server.ts", "dev:tsx": "dotenv -e ../../apps/web/.env.local -- tsx watch src/servers/node-server.ts", "dev:bun": "dotenv -e ../../apps/web/.env.local -- bun run --watch src/servers/bun-server.ts", "start": "npm run start:esbuild", "start:esbuild": "npm run build:node && dotenv -e ../../apps/web/.env.local -- node dist/server.js", "start:doc": "npm run build:doc && dotenv -e ../../apps/web/.env.local -- node dist/server-doc.js", "start:bun:exe": "dotenv -e ../../apps/web/.env.local -- ./src/servers/bika-server-bun", "start:bun": "dotenv -e ../../apps/web/.env.local -- bun run build/bun-server.js"}}