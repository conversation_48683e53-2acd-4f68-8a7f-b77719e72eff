import { SITEMAP_HOSTNAME } from '@bika/contents/config/client/sitemap';
import { unsafeGetDictionary } from '@bika/contents/i18n/dictionaries';
import { getAllHelps } from '../sitemap/[lang]/help-sitemap.xml/route';

const dict = unsafeGetDictionary('en');

/**
 * Generates a list of help pages in markdown format
 *
 * @returns {Promise<string>} A markdown formatted string containing all help pages
 */
async function genHelpsPagesList() {
  const helps = await getAllHelps();
  let txt = ``;
  for (const help of helps) {
    if (help.metadata?.sidebar_label)
      txt += `- [${help.metadata?.sidebar_label}](${SITEMAP_HOSTNAME}/${help.lang}/help/${help.slugs.join('/')})\n`;
  }
  return txt;
}

/**
 * Generates a markdown document containing information about Bika.ai platform,
 * including its features, templates, help resources, and social media links.
 *
 * @returns {Promise<string>} A markdown formatted string containing Bika.ai platform information
 */
export async function genLlmsTxt(_isFull: boolean) {
  const helpPagesText = await genHelpsPagesList();
  const llmsTxt = `# ${dict.slogan.slogan_title}

> ${dict.slogan.slogan_prd_xl} 

Bika.ai is the first AI organizer. Chat, build, manage agentic AI teams like a messenger app.

Vibe working with your marketer AI agents, customers databases, support documents, email automations, sales dashboard to run a one-person AI company. 

With Bika.ai, you can build your own one-person AI company and handle thousands tasks and customize everything to suit your needs.

## What is **AI Organizer**?

The path to AGI has five levels: chatbot, reasoner, agent, innovator, and organizer.

Bika.ai's goal is to create an AI organizer for superhumans that can build Level 5 AGI organizations.

Bika.ai is the AI organizer that can build agentic apps that bundles with AI models, API tools, billion-row spreadsheet-databases, automation workflows, form, dashboard, AI chatbots, along with intelligent missions and reports generation.

This unified "Agentic Apps" is designed to automate data-critical jobs in marketing, sales, project management, operations, and other business domains.

## Agentic AI Apps & Templates & Marketplace

- [Templates Center](https://bika.ai/template)
- [Integrations](https://bika.ai/integration)
- [Create Apps with AI](https://bika.ai/ai-app-builder)

## Help Center & Documentation & Cookbook

- [Help Center / Tutorial ](https://bika.ai/help)
- [What is Bika.ai? Quick Starter Guide](https://bika.ai/blog/what-is-bika-ai)
- [OpenAPI SDK Tutorial](https://bika.ai/help/guide/developer/openapi)
- [OpenAPI Documentaion](https://bika.ai/help/openapi/bika)
- [OpenAPI Specification](https://bika.ai/api/openapi/bika/openapi.json)
${helpPagesText}

## Resources

- [Getting Started. It’s FREE](https://bika.ai/space)
- [Contact Sales](https://staging.bika.ltd/space/spcW24DmHJwAmMLlm62waIbz/node/fomiDK3u7gjsqitSWyENnbv2)
- [Pricing](https://bika.ai/pricing)
- [Roadmap](https://bika.ai/roadmap)
- [Brand Assets](https://staging.bika.ltd/space/spcW24DmHJwAmMLlm62waIbz/node/datvNcXYYPGZ7ycHp84EJFBN)
- [Privacy Policy](https://bika.ai/privacy)
- [Terms of Service](https://bika.ai/terms-of-service)
- [Contact Customer Service](https://bika.ai/contact-service)
- [About us](https://bika.ai/about-us)

## Languages

- [English](https://bika.ai/en/)
- [简体中文](https://bika.ai/zh-CN/)
- [繁體中文](https://bika.ai/zh-TW/)
- [日本語](https://bika.ai/ja/)

## Social Media

- [Bika.ai X(Twitter)](http://x.com/bika-ai)
- [Bika.ai Github](https://github.com/bika-ai/bika.ai)
`;

  return llmsTxt;
}

export async function GET() {
  const llmsTxt = await genLlmsTxt(false);
  return new Response(llmsTxt, {
    headers: {
      'Content-Type': 'text/plain; charset=UTF-8',
    },
  });
}
