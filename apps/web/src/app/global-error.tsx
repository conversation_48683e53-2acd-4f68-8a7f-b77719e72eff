'use client';

import ErrorPage from './error';

// import * as Sentry from '@sentry/nextjs';
// import React, { useEffect } from 'react';

export default function GlobalError(props: { error: Error & { digest?: string } }) {
  return (
    <html lang="en">
      <body>
        <ErrorPage {...props} />
        {/* <h2>something went wrong!</h2>
        <button onClick={() => reset()}>try again</button> */}
      </body>
    </html>
  );
}

// export default ErrorPage;
