'use client';

import { useLocale } from '@bika/contents/i18n/context';
import * as utils from '@bika/domains/shared/client/utils';
import { Button } from '@bika/ui/button';
import { StandalonePage } from '@bika/ui/components/standalone/index';
import { Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import * as Sentry from '@sentry/nextjs';
import Image from 'next/image';
import { useEffect } from 'react';

export default function AppErrorPage(props: { error: Error & { digest?: string } }) {
  const { t } = useLocale();
  const { error } = props;
  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  return (
    <StandalonePage>
      <Stack mx={12} my={12} direction="column" alignItems="center">
        <Image src="/assets/images/error.png" width={200} height={200} alt="" />
        <Stack
          direction="column"
          sx={{
            maxWidth: 340,
            // overflow: 'hidden',
          }}
        >
          <Typography
            mb={2}
            level="h6"
            sx={{
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
            }}
          >
            {error.message}
          </Typography>
          <Typography
            // textAlign="center"
            level="b3"
            sx={{
              // 强制换行
              whiteSpace: 'pre-wrap',
            }}
          >
            {t.global.error_description}
          </Typography>
          <ul>
            {t.global.error_reason.map((reason, index) => (
              <li
                style={{
                  marginLeft: 16,
                  listStyleType: 'initial',
                }}
                key={index}
              >
                <Typography display="inline" component="span" level="b3">
                  {reason}
                </Typography>
              </li>
            ))}
          </ul>

          <Stack sx={{ marginTop: 4 }}>
            <Button
              onClick={() => {
                utils.smartGoBack();
              }}
            >
              {t.error.back}
            </Button>
          </Stack>
        </Stack>
      </Stack>
    </StandalonePage>
  );
}
