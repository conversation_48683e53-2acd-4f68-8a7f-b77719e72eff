{"name": "@bika/web", "version": "2.0.1-alpha.14", "dependencies": {"@ant-design/nextjs-registry": "^1.0.0", "@bika/api-caller": "workspace:*", "@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/server": "workspace:*", "@bika/server-orm": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@emoji-mart/data": "^1.2.1", "@floating-ui/dom": "1.6.12", "@floating-ui/react": "0.26.28", "@formatjs/intl-localematcher": "^0.5.2", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.1.1", "@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "@mui/joy": "^5.0.0-beta.52", "@mui/x-date-pickers": "7.16.0", "@mui/x-date-pickers-pro": "^7.16.0", "@next/mdx": "15.3.3", "@node-rs/argon2": "2.0.2", "@sentry/integrations": "^7.18.0", "@sentry/nextjs": "^7.18.0", "@sentry/tracing": "^7.18.0", "@tailwindcss/line-clamp": "0.4.4", "@tanstack/react-query-devtools": "^5.51.15", "@trpc/client": "^11.4.4", "@vercel/analytics": "^1.1.2", "@vercel/otel": "^1.8.2", "@vercel/speed-insights": "^1.0.7", "@vvo/tzdb": "^6.125.0", "ahooks": "^3.7.10", "basenext": "workspace:*", "bika.ai": "workspace:*", "cheerio": "1.0.0-rc.12", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.0", "core-js": "^3.37.1", "dayjs": "1.11.10", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "fuse.js": "^7.0.0", "googleapis": "^148.0.0", "gray-matter": "^4.0.3", "immer": "^10.0.3", "lodash": "^4.17.21", "lucide-react": "0.542.0", "mobx": "^6.12.4", "negotiator": "^0.6.3", "next": "15.3.3", "next-mdx-remote": "^4.4.1", "notistack": "^3.0.1", "path-browserify": "^1.0.1", "rc-util": "5.24.4", "react": "18.3.1", "react-dom": "18.3.1", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.10.3", "server-only": "^0.0.1", "sharelib": "workspace:*", "simplebar-react": "^3.2.5", "string-ts": "^2.1.1", "styled-components": "^6.1.11", "tailwind-merge": "^2.2.0", "url-join": "^5.0.0", "urlcat": "^3.1.0", "xgplayer": "^2.20.12", "zod": "^3.25.0", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@next/bundle-analyzer": "15.3.3", "@opentelemetry/api-logs": "^0.55.0", "@playwright/experimental-ct-react": "^1.50.0", "@playwright/test": "^1.50.0", "@svgr/webpack": "^8.1.0", "@tauri-apps/cli": "^1.5.14", "@types/mdx": "^2.0.10", "@types/negotiator": "^0.6.3", "@types/node": "^20.11.24", "@types/node-cron": "^3.0.11", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react-swc": "^3.11.0", "@welldone-software/why-did-you-render": "^8.0.3", "autoprefixer": "^10.4.16", "dotenv-cli": "^7.3.0", "jsdom": "^23.0.1", "next-routes-list": "^1.1.5", "postcss": "^8.4.32", "postcss-nesting": "^12.1.5", "sharp": "^0.34.0", "typescript": "^5.8.3", "vercel": "latest", "vitest": "^3.2.4"}, "private": true, "scripts": {"build:rsc": "NODE_OPTIONS=--max-old-space-size=8192 RSC_ENABLED=true next build", "build": "NODE_OPTIONS=--max-old-space-size=8192 next build --experimental-build-mode compile && npm run _build-prisma", "build:compile": "NODE_OPTIONS=--max-old-space-size=8192 next build --experimental-build-mode compile && npm run _build-prisma", "build:generate": "NODE_OPTIONS=--max-old-space-size=8192 next build --experimental-build-mode generate && npm run _build-prisma", "_build-prisma": "mkdir -p ./.next/standalone/node_modules/.prisma/client && cp -r ../../packages/bika-server-orm/prisma/prisma-client/* ./.next/standalone/node_modules/.prisma/client", "build-analyze": "ANALYZE_WEBPACK=true next build", "generate-next-routes-list": "cd src && npx generate-next-routes-list", "postinstall": "pnpm run generate-next-routes-list", "build:local": "next build --experimental-build-mode compile", "dev": "NODE_OPTIONS=--max-old-space-size=10240 next dev", "dev:debug": "NODE_OPTIONS='--max-old-space-size=10240 --inspect' next dev", "dev:turbo": "USE_TURBOPACK=true NODE_OPTIONS=--max-old-space-size=10240 next dev --turbopack", "dev:turbo:rsc": "RSC_ENABLED=true USE_TURBOPACK=true NODE_OPTIONS=--max-old-space-size=10240 next dev --turbopack", "dev:wdyr": "WDYR_ENABLED=true next dev", "check": "NODE_OPTIONS=--max-old-space-size=8192 tsc --noEmit", "lint": "biome format . --write && biome check .", "react:scan": "npx react-scan http://localhost:3000", "start": "next start", "test": "dotenv -e .env.local -- vitest", "test:dir": "dotenv -e .env.local -- vitest ./test --run", "test:ct": "playwright test -c playwright-ct.config.ts", "test:ct:report": "playwright show-report ", "test:playwright": "playwright test -c playwright.config.ts"}}