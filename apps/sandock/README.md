
# Sandock.ai

Sandbox in Docker for AI Agents — Next.js + Hono API.

## Stack

- Next.js 15 (App Router)
- Hono (shared API re-export from `@toolsdk.ai/domain`)
- (Docs framework TBD: Nextra / Fumadocs – Docusaurus removed)

## Use Cases

- Code Interpreter: Execute user code (REPL / scripts) in an isolated, resource‑limited container.
- File System Sandbox: Read and write project files within a scoped workspace volume.
- MCP Server Host: Run a Model Context Protocol server instance for agent tool integration.
- VS Code (Web): Launch VS Code Server (default image exposes port 8080) for in‑browser editing.
- Static Deploy Preview: Serve AI‑generated static HTML/CSS/JS on port 8080 for rapid preview and sharing.

## Dev Quick Start

```bash
pnpm -F sandock dev           # Next.js on :3030
```

Visit:
- App: http://localhost:3030/
- Hono API (shared): http://localhost:3030/api/trpc (tRPC) / other `/api/*`
	(Docs path pending new framework integration)

## 文档框架迁移说明

Docusaurus 已移除。后续将评估并接入：

### 方案对比（Nextra vs Fumadocs）

| 维度                  | Nextra                                | Fumadocs                       |
| --------------------- | ------------------------------------- | ------------------------------ |
| 基础                  | 基于 Next.js, 零/低配置               | 基于 Next.js, 内容+组件化极简  |
| MDX 支持              | 完整（内置主题 Docs / Blog）          | 完整，强调渐进式替换 & 组合    |
| i18n                  | 借助 Next.js 原生或自建结构           | 依赖 Next.js i18n 路由         |
| 目录/导航             | 自动侧边栏、自动生成 TOC              | 需要显式配置/组件，更灵活      |
| 搜索                  | 官方无内置，需要 Algolia/自建         | 官方无内置，鼓励自建/第三方    |
| 自定义 UI             | 通过 theme override，可定制但层次较深 | 直接写 React 组件，侵入性低    |
| 社区成熟度            | 较成熟，生态/示例多                   | 新兴，API 更轻量               |
| 动态 & MDX React 融合 | 良好                                  | 更自由（你几乎就在写 Next.js） |
| 学习/维护成本         | 低~中                                 | 低（靠约定少）                 |

初步倾向：若需要快速得到标准文档站 + 多语言/版本化 → 选 Nextra；若更偏向把“文档视为应用的一部分”并高度自定义交互组件 → 选 Fumadocs。

接入计划（示例 - 以 Nextra 为例）：
1. `pnpm add nextra nextra-theme-docs` (或 Fumadocs 对应包)
2. 在 `apps/sandock/` 下建立 `docs/` 目录（或放根项目集中 docs mono 包）
3. 新建 `next.config.mjs` 中集成 `withNextra({ theme, themeConfig })`
4. 通过独立路由前缀 `/docs/*` （或同仓单独部署）
5. 追加搜索（自建：索引 MDX frontmatter + heading JSON → edge/cron 构建）

后续决定后会补充脚本：`docs:dev` / `docs:build`（新的实现）。

## Hono 集成

`app/api/route.ts` 直接 re-export 共享的 Hono handlers：
> 这样自动获得 tRPC + REST 路由，无需再次声明。

## 后续可选

- Docs 版本化：`public/docs/vX.Y/` + symlink `latest`
- Edge Runtime：将 `runtime` 切换为 `edge`（若依赖允许）
- 自定义搜索：改为对 MDX 解析（remark/rehype）生成 heading & metadata JSON，供 `/api/docs/search`。

## Prisma + Clerk 集成

最小 Prisma schema 现已移动到包：`packages/sandock-domains/prisma/schema.prisma`，包含：

```
model User { id String @id ... clerkId String @unique projects Project[] }
model Project { ... }
```

Clerk 登录后访问 `/api/me`，会自动 upsert Clerk 用户到本地 `User` 表。

### 环境变量

在 `apps/sandock/.env.local`（自行创建）添加：

```
PG_DATABASE_URL=postgresql://user:pass@localhost:5432/sandock
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
CLERK_SIGN_IN_URL=/sign-in
CLERK_SIGN_UP_URL=/sign-up
```

运行迁移/生成：

```bash
pnpm -F sandock db:gen
pnpm -F sandock db:migrate
```

### 使用

- `GET /api/me` 返回 `{ user }`（并自动同步）。
- 在 React 组件中可使用 `@clerk/nextjs` hooks：`import { useUser } from '@clerk/nextjs';`

### 扩展建议

- 若需要共享主项目大型 schema，可改用 workspace 的 `@bika/server-orm`，或继续扩展 `@sandock/domains` schema。
- 添加更多模型后记得执行 `db:migrate`。

---

Minimal scaffold; extend as needed.