'use client';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import Card from '@mui/joy/Card';
import Chip from '@mui/joy/Chip';
import Divider from '@mui/joy/Divider';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import Link from 'next/link';
import { sandboxStatusColor } from '../app/dashboard/sandboxes/statusColor';
import { trpc } from '../trpc/react';
import { ClientTerminalButton } from './ClientTerminalButton';

export function SandboxDetailClient({ sandboxId }: { sandboxId: string }) {
  const refreshQuery = trpc.sandbox.refresh.useQuery({ id: sandboxId }, { refetchInterval: 5000 });
  const startMut = trpc.sandbox.start.useMutation({ onSuccess: () => refreshQuery.refetch() });
  const stopMut = trpc.sandbox.stop.useMutation({ onSuccess: () => refreshQuery.refetch() });
  const deleteMut = trpc.sandbox.delete.useMutation({ onSuccess: () => refreshQuery.refetch() });
  const logsQuery = trpc.sandboxOps.logs.useQuery(
    { sandboxId, page: 1, pageSize: 20 },
    { refetchInterval: 10000 },
  );

  type SandboxLite = {
    id: string;
    title: string;
    status: string;
    provider: string;
    createdAt: string | Date;
    updatedAt: string | Date;
    image?: string | null;
    cpuLimit?: number | null;
    memoryLimit?: number | null;
    lastHeartbeatAt?: Date | string | null;
    lastStartedAt?: Date | string | null;
    lastStoppedAt?: Date | string | null;
    accumulatedRunMs?: number | null;
    lastStartupDurationMs?: number | null;
    providerRef?: string | null;
    metadata?: unknown;
  };
  const sb = (refreshQuery.data as SandboxLite | undefined) ?? undefined;

  const loading = refreshQuery.isLoading;
  const error = refreshQuery.error;

  return (
    <Stack spacing={3}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
        <Typography level="h3" sx={{ fontWeight: 600, flex: 1 }}>
          Sandbox 详情
        </Typography>
        <Button size="sm" variant="outlined" component={Link} href="/dashboard/sandboxes">
          返回列表
        </Button>
      </Box>
      {loading && (
        <Sheet variant="outlined" sx={{ p: 4, textAlign: 'center', borderRadius: 'sm' }}>
          <Typography level="body-sm">加载中...</Typography>
        </Sheet>
      )}
      {error && (
        <Sheet variant="soft" color="danger" sx={{ p: 3, borderRadius: 'sm' }}>
          <Typography level="body-sm">加载失败：{error.message}</Typography>
        </Sheet>
      )}
      {!loading && !error && !sb && (
        <Sheet variant="soft" color="warning" sx={{ p: 3, borderRadius: 'sm' }}>
          <Typography level="body-sm">未找到该 Sandbox。</Typography>
        </Sheet>
      )}
      {sb && (
        <Card variant="outlined" sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1.5 }}>
          <Typography level="title-lg" sx={{ fontWeight: 600 }}>
            {sb.title}
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            <Chip size="sm" variant="soft" color="neutral">
              ID: {sb.id}
            </Chip>
            <Chip size="sm" variant="soft" color={sandboxStatusColor(sb.status)}>
              {sb.status}
            </Chip>
            <Chip
              size="sm"
              variant="soft"
              color={
                sb.provider === 'DOCKER'
                  ? 'warning'
                  : sb.provider === 'KUBERNETES'
                    ? 'primary'
                    : 'neutral'
              }
            >
              {sb.provider}
            </Chip>
            {sb.image && (
              <Chip size="sm" variant="soft" color="neutral">
                镜像: {sb.image}
              </Chip>
            )}
            {typeof sb.cpuLimit === 'number' && (
              <Chip size="sm" variant="soft" color="neutral">
                CPU: {sb.cpuLimit}
              </Chip>
            )}
            {typeof sb.memoryLimit === 'number' && (
              <Chip size="sm" variant="soft" color="neutral">
                内存: {sb.memoryLimit} MB
              </Chip>
            )}
            {sb.providerRef && (
              <Chip size="sm" variant="soft" color="neutral">
                Ref: {sb.providerRef.slice(0, 12)}
              </Chip>
            )}
          </Stack>
          <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
            创建时间: {new Date(sb.createdAt).toLocaleString()} | 更新时间:{' '}
            {new Date(sb.updatedAt).toLocaleString()}
          </Typography>
          <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
            最近心跳: {sb.lastHeartbeatAt ? new Date(sb.lastHeartbeatAt).toLocaleString() : '—'} |
            最近启动: {sb.lastStartedAt ? new Date(sb.lastStartedAt).toLocaleString() : '—'} |
            最近停止: {sb.lastStoppedAt ? new Date(sb.lastStoppedAt).toLocaleString() : '—'}
          </Typography>
          <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
            累计运行时间(ms): {sb.accumulatedRunMs ?? 0} | 最近启动耗时(ms):{' '}
            {sb.lastStartupDurationMs ?? '—'}
          </Typography>
          <Stack direction="row" spacing={1} sx={{ mt: 1, flexWrap: 'wrap' }}>
            {sb.status !== 'RUNNING' && sb.status !== 'DELETING' && sb.status !== 'DELETED' && (
              <Button
                size="sm"
                variant="outlined"
                color="success"
                loading={startMut.isPending}
                onClick={() => startMut.mutate({ id: sb.id })}
              >
                启动
              </Button>
            )}
            {sb.status === 'RUNNING' && (
              <Button
                size="sm"
                variant="outlined"
                color="warning"
                loading={stopMut.isPending}
                onClick={() => stopMut.mutate({ id: sb.id })}
              >
                停止
              </Button>
            )}
            {sb.status === 'RUNNING' && <ClientTerminalButton sandboxId={sb.id} />}
            {sb.status !== 'DELETING' && sb.status !== 'DELETED' && (
              <Button
                size="sm"
                variant="plain"
                color="danger"
                loading={deleteMut.isPending}
                onClick={() => {
                  if (confirm('确认删除? 此操作不可恢复。')) deleteMut.mutate({ id: sb.id });
                }}
              >
                删除
              </Button>
            )}
          </Stack>
        </Card>
      )}
      {sb && (
        <Card variant="outlined" sx={{ p: 2 }}>
          <Typography level="title-sm" sx={{ mb: 1 }}>
            最近执行日志
          </Typography>
          {logsQuery.isLoading && <Typography level="body-xs">加载日志...</Typography>}
          {!logsQuery.isLoading && (logsQuery.data?.items?.length ?? 0) === 0 && (
            <Typography level="body-xs" color="neutral">
              暂无日志
            </Typography>
          )}
          <Stack spacing={1}>
            {(logsQuery.data?.items ?? []).map((raw) => {
              interface LogItem {
                id: string;
                type: string;
                language?: string | null;
                command?: string | null;
                codeSnippet?: string | null;
                exitCode?: number | null;
                durationMs?: number | null;
                stdoutPreview?: string | null;
                stderrPreview?: string | null;
                createdAt: string | Date;
              }
              const log = raw as LogItem;
              return (
                <Sheet
                  key={log.id}
                  variant="soft"
                  sx={{
                    p: 1,
                    borderRadius: 'sm',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 0.5,
                  }}
                >
                  <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ alignItems: 'center' }}>
                    <Chip size="sm" variant="soft" color="neutral">
                      {log.type}
                    </Chip>
                    {log.language && (
                      <Chip size="sm" variant="soft" color="neutral">
                        {log.language}
                      </Chip>
                    )}
                    {typeof log.exitCode === 'number' && (
                      <Chip
                        size="sm"
                        variant="soft"
                        color={log.exitCode === 0 ? 'success' : 'danger'}
                      >
                        退出 {log.exitCode}
                      </Chip>
                    )}
                    {typeof log.durationMs === 'number' && (
                      <Chip size="sm" variant="soft" color="neutral">
                        {log.durationMs} ms
                      </Chip>
                    )}
                    <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
                      {new Date(log.createdAt).toLocaleString()}
                    </Typography>
                  </Stack>
                  {log.command && (
                    <Typography
                      level="body-xs"
                      sx={{ fontFamily: 'ui-monospace, SFMono-Regular, Menlo, monospace' }}
                    >
                      $ {log.command}
                    </Typography>
                  )}
                  {log.codeSnippet && (
                    <Typography
                      level="body-xs"
                      sx={{
                        fontFamily: 'ui-monospace, SFMono-Regular, Menlo, monospace',
                        whiteSpace: 'pre-wrap',
                      }}
                    >
                      {log.codeSnippet}
                    </Typography>
                  )}
                  {(log.stdoutPreview || log.stderrPreview) && (
                    <Box sx={{ mt: 0.5 }}>
                      {log.stdoutPreview && (
                        <Typography
                          level="body-xs"
                          sx={{
                            fontFamily: 'ui-monospace, SFMono-Regular, Menlo, monospace',
                            whiteSpace: 'pre-wrap',
                            color: 'success.600',
                          }}
                        >
                          {log.stdoutPreview}
                        </Typography>
                      )}
                      {log.stderrPreview && (
                        <Typography
                          level="body-xs"
                          sx={{
                            fontFamily: 'ui-monospace, SFMono-Regular, Menlo, monospace',
                            whiteSpace: 'pre-wrap',
                            color: 'danger.600',
                          }}
                        >
                          {log.stderrPreview}
                        </Typography>
                      )}
                    </Box>
                  )}
                </Sheet>
              );
            })}
          </Stack>
        </Card>
      )}
      {sb && (
        <Card variant="outlined" sx={{ p: 2 }}>
          <Typography level="title-sm" sx={{ mb: 1 }}>
            快速终端 (Mock)
          </Typography>
          <ClientTerminalButton sandboxId={sandboxId} />
          <Divider sx={{ my: 1 }} />
          <Typography level="body-xs" color="neutral" sx={{ maxWidth: 600 }}>
            这里是一个占位终端按钮。后续可以在此集成文件浏览、实时资源使用等功能。
          </Typography>
        </Card>
      )}
    </Stack>
  );
}
