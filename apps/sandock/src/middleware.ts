import { clerkMiddleware } from '@clerk/nextjs/server';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

// Middleware now only handles authentication & access control.
// Static docs rewriting removed (Docusaurus eliminated). When a new docs framework
// is integrated, add only the minimal required logic (likely none if using /docs route segment).

const PUBLIC_AUTH_PATHS = ['/sign-in', '/sign-up'];

export const middleware = clerkMiddleware(async (auth, req: NextRequest) => {
  const { pathname } = req.nextUrl;

  // Auth gate for /dashboard
  if (pathname.startsWith('/dashboard')) {
    const isPublic = PUBLIC_AUTH_PATHS.some((p) => pathname === p || pathname.startsWith(p + '/'));
    if (!isPublic) {
      const { userId } = await auth();
      if (!userId) {
        const url = req.nextUrl.clone();
        url.pathname = '/sign-in';
        url.searchParams.set('redirect_url', pathname + req.nextUrl.search);
        return NextResponse.redirect(url);
      }
    }
    return NextResponse.next();
  }

  // Public auth routes
  if (PUBLIC_AUTH_PATHS.some((p) => pathname === p || pathname.startsWith(p + '/'))) {
    return NextResponse.next();
  }

  return NextResponse.next();
});

export const config = {
  matcher: [
    // Apply Clerk to all application pages (exclude Next.js internals & static assets implicitly)
    '/((?!_next/|static/|favicon.ico|robots.txt).*)',
    '/api/me',
    '/api/trpc/:path*',
    '/api/billing/:path*',
  ],
};
