import type { NextRequest } from 'next/server';

export const runtime = 'edge';

// Basic sitemap; add '/docs/' entries after new docs framework integration.
export async function GET(_req: NextRequest) {
  const base = 'https://sandock.ai';
  const urls = [''];
  const now = new Date().toISOString();
  const xml =
    `<?xml version="1.0" encoding="UTF-8"?>\n` +
    `<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">` +
    urls
      .map(
        (u) =>
          `\n  <url><loc>${base}/${u}</loc><lastmod>${now}</lastmod><changefreq>daily</changefreq><priority>0.7</priority></url>`,
      )
      .join('') +
    `\n</urlset>`;
  return new Response(xml, {
    headers: {
      'Content-Type': 'application/xml; charset=utf-8',
      'Cache-Control': 'public, max-age=3600',
    },
  });
}
