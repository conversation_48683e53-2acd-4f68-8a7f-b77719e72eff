import { auth } from '@clerk/nextjs/server';
import Sheet from '@mui/joy/Sheet';
import Typography from '@mui/joy/Typography';
import type { Metadata } from 'next';
import { SandboxDetailClient } from '../../../../components/SandboxDetailClient';
import { ensureUserByClerkId } from '../../../../lib/ensureUser';

export const metadata: Metadata = {
  title: 'Sandbox 详情',
};

interface Props {
  params: Promise<{ sandboxId: string }>;
}

export default async function SandboxDetailPage({ params }: Props) {
  const { userId } = await auth();
  if (!userId) {
    return (
      <Sheet variant="soft" sx={{ p: 3, borderRadius: 'sm' }}>
        <Typography level="body-md">请先登录。</Typography>
      </Sheet>
    );
  }
  const user = await ensureUserByClerkId(userId);
  if (!user) {
    return (
      <Sheet variant="soft" color="danger" sx={{ p: 3, borderRadius: 'sm' }}>
        <Typography level="body-md">用户同步失败，请刷新或重试。</Typography>
      </Sheet>
    );
  }
  const { sandboxId } = await params;
  return <SandboxDetailClient sandboxId={sandboxId} />;
}
