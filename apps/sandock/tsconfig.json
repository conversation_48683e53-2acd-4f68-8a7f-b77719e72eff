{"compilerOptions": {"jsx": "preserve", "allowJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "module": "esnext", "target": "esnext", "skipLibCheck": true, "types": ["node"], "baseUrl": ".", "lib": ["dom", "dom.iterable", "esnext"], "strict": false, "noEmit": true, "incremental": true, "plugins": [{"name": "next"}], "strictNullChecks": true}, "include": ["next-env.d.ts", "src/**/*", ".next/types/**/*.ts"], "exclude": ["node_modules"]}