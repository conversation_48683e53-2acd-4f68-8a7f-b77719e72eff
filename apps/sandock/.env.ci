PG_DATABASE_URL="postgresql://bika:bikabika@127.0.0.1:5432/sandock?schema=public&connection_limit=1"
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_ZnVuLWRydW0tNjAuY2xlcmsuYWNjb3VudHMuZGV2JA
CLERK_SECRET_KEY=sk_test_LoYSzVxjTm6QbIxMphPIe5hTwPJmG4gmiMH3XyonE1
CLERK_SIGN_IN_URL=/sign-in
CLERK_SIGN_UP_URL=/sign-up
STRIPE_PUBLIC_KEY=pk_test_51S20CIEIpkZKWNtfs9xXMcrIsvSanG5cbjMitLX6Ijvo7M24ZkFrZ9vvb4ZG7cgFuzZzgBVpruxbReHKVQQV4oyI00kIbLovQy
STRIPE_SECRET_KEY=sk_test_51S20CIEIpkZKWNtfm3RxEl0KUkZHZgJ3fCkK5VkfeohjD4bpPlBmWTpKdxhYbNfhGHhPp6fRk1D3d92siKRvxx7b00dVITMaaM

SANDBOX_PROVIDER=DOCKER

# --- Kubernetes Sandbox (switch by setting SANDBOX_PROVIDER=KUBERNETES) ---
# SANDBOX_K8S_IN_CLUSTER=true                     # Use in-cluster config (inside a Pod)
# SANDBOX_K8S_SERVER=https://your-apiserver:6443  # API server address (when not in-cluster)
# SANDBOX_K8S_CA_DATA=BASE64_CA                   # certificate-authority-data
# SANDBOX_K8S_SKIP_TLS_VERIFY=false               # set true to skip TLS verify (not recommended)
# SANDBOX_K8S_TOKEN=REDACTED_TOKEN                # Bearer token (ServiceAccount or user)
# SANDBOX_K8S_CLIENT_CERT=BASE64_CERT             # Optional: client cert (mutually exclusive with token)
# SANDBOX_K8S_CLIENT_KEY=BASE64_KEY               # Optional: client key
# SANDBOX_K8S_USERNAME=                           # Optional basic auth username
# SANDBOX_K8S_PASSWORD=                           # Optional basic auth password
# SANDBOX_K8S_NAMESPACE=default                   # Target namespace for ephemeral pods
# SANDBOX_K8S_IMAGE=node:20-bullseye              # Sandbox container image (default)
# SANDBOX_K8S_WORKDIR=/sandbox                    # Working directory inside pod
# SANDBOX_K8S_MEMORY_MB=512                       # Memory limit MiB
# SANDBOX_K8S_CPU_LIMIT=500m                      # CPU limit
# SANDBOX_K8S_SERVICE_ACCOUNT=                    # ServiceAccount name (if needed)
# SANDBOX_K8S_PULL_POLICY=IfNotPresent            # Image pull policy (IfNotPresent|Always)
# SANDBOX_K8S_IDLE_SECONDS=3600                   # Sleep duration to keep pod alive
# SANDBOX_K8S_POD_NAME=                           # Fixed pod name (omit for random)
# SANDBOX_K8S_CONTEXT_NAME=env-ctx                # (Optional) context name label
# SANDBOX_K8S_CLUSTER_NAME=env-cluster            # (Optional) cluster name label
# SANDBOX_K8S_USER_NAME=env-user                  # (Optional) user name label

# --- Docker Sandbox (switch by setting SANDBOX_PROVIDER=DOCKER) ---
# SANDBOX_DOCKER_IMAGE=node:20-bullseye           # Default Base image
# SANDBOX_DOCKER_PY_IMAGE=python:3.12-slim        # Python image when running python code
# SANDBOX_DOCKER_PULL=false                       # Pull image if missing
# SANDBOX_DOCKER_WORKDIR=/sandbox                 # Working directory
# SANDBOX_DOCKER_MEMORY_MB=512                    # Memory limit in MiB
# SANDBOX_DOCKER_CPU=0.5                          # vCPU cores (fractional). Converts to cpuShares
# SANDBOX_DOCKER_CPU_SHARES=256                   # Alternative to SANDBOX_DOCKER_CPU (raw shares)
# SANDBOX_DOCKER_AUTO_REMOVE=true                 # Auto remove container on stop

# --- Local Sandbox (switch by setting SANDBOX_PROVIDER=LOCAL) ---
# SANDBOX_LOCAL_CPU=2                             # Override detected logical cores fraction used for billing (default: half host cores, min 0.25)
# SANDBOX_LOCAL_MEM_MB=1024                       # Override heuristic memory cap for billing (default: 25% host mem, min 256)