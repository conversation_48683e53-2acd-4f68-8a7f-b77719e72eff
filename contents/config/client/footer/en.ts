import type { FooterColumn } from './types';

export default [
  {
    name: 'Product',
    items: [
      {
        name: 'Login & Register',
        href: '/auth',
      },
      {
        name: 'Template Center',
        href: '/template',
      },
      {
        name: 'Integration Apps',
        href: '/integration',
      },
      {
        name: 'Skillsets (AI Tools)',
        href: '/skillset',
      },
      {
        name: 'AI Models',
        href: '/ai-models',
      },
      {
        name: 'API Documentation',
        href: '/help/guide/developer/openapi',
      },
    ],
  },
  {
    name: 'Support',
    items: [
      {
        name: 'Help Center',
        href: '/help',
      },
      {
        name: 'Tutorial',
        href: '/help/index',
      },
      {
        name: 'Roadmap',
        href: '/roadmap',
      },
      {
        name: 'Brand Assets',
        href: 'https://staging.bika.ltd/space/spcW24DmHJwAmMLlm62waIbz/node/datvNcXYYPGZ7ycHp84EJFBN',
      },
      {
        name: 'Privacy Policy',
        href: '/privacy',
      },
      {
        name: 'Terms of Service',
        href: '/terms-of-service',
      },
    ],
  },
  {
    name: 'Contacts',
    items: [
      {
        name: 'Community',
        href: 'https://community.bika.ai',
      },
      {
        name: 'Contact Sales',
        href: 'https://staging.bika.ltd/space/spcW24DmHJwAmMLlm62waIbz/node/fomiDK3u7gjsqitSWyENnbv2',
      },
      // {
      //   name: '<EMAIL>',
      // },
    ],
  },
  {
    name: 'Company',
    items: [
      {
        name: 'Affiliate',
        href: '/affiliate',
      },
      {
        name: 'About us',
        href: '/about-us',
      },
      // {
      //   name: 'Status',
      //   href: 'https://bika.betteruptime.com/',
      // },
    ],
  },
  {
    name: 'Language',
    items: [
      {
        name: 'English',
        href: '/en',
        appendPath: true,
      },
      {
        name: '简体中文',
        href: '/zh-CN',
        appendPath: true,
      },
      {
        name: '繁體中文',
        href: '/zh-TW',
        appendPath: true,
      },
      {
        name: '日本語',
        href: '/ja',
        appendPath: true,
      },
    ],
  },
] as FooterColumn[];
