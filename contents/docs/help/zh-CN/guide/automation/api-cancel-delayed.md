---
title: 如何通过通过 API 中断 延迟执行器Delay 的执行？
slug: help/zh-CN/guide/automation/api-cancel-delayed
sidebar_position: 90
sidebar_label: 如何通过通过 API 中断 Delay 的执行？
---


# 如何通过通过 API 中断 延迟执行器Delay 的执行？

## 方式一：OpenAPI

- 使用者：自动化维护者。
    - 由维护者主动控制业务流程，选择性取消

- API docs: https://dev.bika.ltd/zh-CN/help/openapi/bika#tag/Automation/paths/~1v1~1automations~1runs~1:runId~1cancel/post
（上线后，https://dev.bika.ltd 域名可换成 https://bika.ai ）
    - 所需参数：自动化运行ID
    - 开发者权限要求：需要有自动化节点权限

### 如何获取自动化运行ID

1. 在自动化中，通过"/"唤起变量 - 自动化 - 运行ID
    1. 每一次运行，都会产生一个全局唯一的运行ID

<img src="https://book.bika.ai/bika-content/assets/OWUrbKM54oS6OaxX119l9G0cg5g.png" src-width="604" src-height="242" align="center"/>

1. 自动化节点 - 运行历史

## 方式二：动态的中断请求URL

- 使用者：（拿到URL的）所有人
    - URL自带鉴权令牌，可免登陆直接调用触发
    - 🌰 邮件中设计了【取消】按钮，接收者可以主动触发后，中断后续流程

- 获取途径：在自动化中，通过"/"唤起变量 - 自动化 - 中断延迟自动化请求URL
    - 支持请求方式：Get、Post
    - 同运行ID类似，每一次运行，都会产生唯一的动态URL

